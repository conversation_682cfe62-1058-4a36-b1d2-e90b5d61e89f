<?php
/**
 * Standalone Banner Test
 * Independent test that doesn't rely on dashboard components
 */

// Define LENDSWIFT constant to prevent direct access to included files
define('LENDSWIFT', true);

// Include initialization file
require_once 'includes/init.php';

// Check if user is logged in
if (!is_user_logged_in()) {
    echo "Please log in first: <a href='login.php'>Login</a>";
    exit;
}

$user_name = $_SESSION['user_name'] ?? 'User';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Standalone Banner Test - <?php echo SITE_NAME; ?></title>
    
    <!-- Notification Banner CSS -->
    <link rel="stylesheet" href="<?php echo ASSETS_URL; ?>/css/notification-banner.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 12px 20px;
            margin: 8px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        .btn:hover {
            background: #4338ca;
        }
        .btn.success { background: #10b981; }
        .btn.danger { background: #ef4444; }
        .btn.info { background: #3b82f6; }
        .btn.warning { background: #f59e0b; }
        
        .test-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
            border-left: 4px solid #4f46e5;
        }
        .log {
            background: #1f2937;
            color: #f9fafb;
            padding: 20px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            min-height: 150px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
        .dashboard-layout {
            /* Fake dashboard layout for testing */
            display: block;
        }
    </style>
</head>
<body>
    <!-- Fake dashboard layout div for banner system detection -->
    <div class="dashboard-layout" style="display: none;"></div>
    
    <div class="test-container">
        <h1>🧪 Standalone Notification Banner Test</h1>
        
        <div class="test-info">
            <h3>Welcome, <?php echo htmlspecialchars($user_name); ?>!</h3>
            <p>This is a standalone test for the notification banner system. It doesn't depend on dashboard components.</p>
            <p><strong>User Type:</strong> <span id="user-type">Detecting...</span></p>
            <p><strong>Current Unread Notifications:</strong> <span id="notification-count">Loading...</span></p>

            <!-- Hidden notification badge for testing -->
            <div style="display: none;">
                <span class="notification-badge"><?php
                    try {
                        $user_id = get_current_user_id();
                        echo get_unread_notifications_count($user_id);
                    } catch (Exception $e) {
                        echo '0';
                    }
                ?></span>
            </div>
        </div>
        
        <h3>Test Controls:</h3>
        <div style="margin-bottom: 20px;">
            <button class="btn" onclick="testNewNotification()">
                <i class="fas fa-bell"></i> New Notification
            </button>
            <button class="btn success" onclick="testSuccess()">
                <i class="fas fa-check-circle"></i> Success Banner
            </button>
            <button class="btn danger" onclick="testError()">
                <i class="fas fa-exclamation-triangle"></i> Error Banner
            </button>
            <button class="btn info" onclick="testInfo()">
                <i class="fas fa-info-circle"></i> Info Banner
            </button>
            <button class="btn warning" onclick="testMultiple()">
                <i class="fas fa-layer-group"></i> Multiple Banners
            </button>
        </div>
        
        <h3>Manual Triggers:</h3>
        <div style="margin-bottom: 20px;">
            <button class="btn" onclick="triggerBanner('new', 'Manual new notification')">
                Manual New
            </button>
            <button class="btn success" onclick="triggerBanner('success', 'Manual success message')">
                Manual Success
            </button>
            <button class="btn danger" onclick="triggerBanner('error', 'Manual error message')">
                Manual Error
            </button>
            <button class="btn info" onclick="triggerBanner('info', 'Manual info message')">
                Manual Info
            </button>
            <button class="btn warning" onclick="checkExistingNotifications()">
                <i class="fas fa-search"></i> Check Existing Notifications
            </button>
        </div>
        
        <div class="log" id="log">
            <strong>Test Log:</strong><br>
            Initializing test environment...<br>
        </div>
    </div>

    <!-- Notification Banner JavaScript -->
    <script src="<?php echo ASSETS_URL; ?>/js/notification-banner.js"></script>
    
    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `<span style="color: #10b981;">[${time}]</span> ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function testNewNotification() {
            log('Testing new notification banner...');
            if (window.notificationBanner) {
                window.notificationBanner.showNewNotificationBanner(1);
                log('✓ New notification banner triggered');
            } else {
                log('✗ Banner system not available');
            }
        }
        
        function testSuccess() {
            log('Testing success banner...');
            if (window.notificationBanner) {
                window.notificationBanner.showSuccess('Test operation completed successfully!');
                log('✓ Success banner triggered');
            } else {
                log('✗ Banner system not available');
            }
        }
        
        function testError() {
            log('Testing error banner...');
            if (window.notificationBanner) {
                window.notificationBanner.showError('Test error message for demonstration!');
                log('✓ Error banner triggered');
            } else {
                log('✗ Banner system not available');
            }
        }
        
        function testInfo() {
            log('Testing info banner...');
            if (window.notificationBanner) {
                window.notificationBanner.showInfo('This is a test informational message!');
                log('✓ Info banner triggered');
            } else {
                log('✗ Banner system not available');
            }
        }
        
        function testMultiple() {
            log('Testing multiple banners...');
            if (window.notificationBanner) {
                window.notificationBanner.showInfo('First banner');
                setTimeout(() => window.notificationBanner.showSuccess('Second banner'), 500);
                setTimeout(() => window.notificationBanner.showError('Third banner'), 1000);
                log('✓ Multiple banners triggered with delays');
            } else {
                log('✗ Banner system not available');
            }
        }
        
        // Manual trigger function using events
        function triggerBanner(type, message) {
            log(`Triggering manual ${type} banner...`);
            document.dispatchEvent(new CustomEvent('showNotificationBanner', {
                detail: { type: type, message: message }
            }));
            log(`✓ Manual ${type} banner event dispatched`);
        }

        // Manual check for existing notifications
        function checkExistingNotifications() {
            log('Manually checking for existing notifications...');
            if (window.notificationBanner) {
                const count = window.notificationBanner.updateNotificationCount();
                log(`Found ${count} notifications in sidebar`);

                if (count > 0) {
                    window.notificationBanner.showExistingNotificationBanner(count);
                    log(`✓ Showed banner for ${count} existing notifications`);
                } else {
                    log('No unread notifications found');
                    window.notificationBanner.checkForExistingNotifications();
                }
            } else {
                log('✗ Banner system not available');
            }
        }
        
        // Initialize and monitor
        document.addEventListener('DOMContentLoaded', function() {
            log('DOM loaded, checking banner system...');

            // Show current notification count
            const badge = document.querySelector('.notification-badge');
            if (badge) {
                const count = parseInt(badge.textContent) || 0;
                document.getElementById('notification-count').textContent = count;
                log(`Current unread notifications: ${count}`);

                if (count > 0) {
                    log(`User has ${count} unread notifications - banner should appear automatically`);
                }
            } else {
                log('No notification badge found in DOM');
                document.getElementById('notification-count').textContent = '0';
            }

            // Check for dashboard layout
            const dashboardLayout = document.querySelector('.dashboard-layout');
            log(`Dashboard layout found: ${dashboardLayout ? 'Yes' : 'No'}`);

            setTimeout(() => {
                if (window.notificationBanner) {
                    log('✓ Banner system initialized successfully');
                    const userType = window.notificationBanner.isAdmin ? 'Admin' : 'User';
                    document.getElementById('user-type').textContent = userType;
                    log(`User type detected: ${userType}`);

                    // Show current notification count from banner system
                    const bannerCount = window.notificationBanner.lastNotificationCount;
                    log(`Banner system detected ${bannerCount} notifications`);

                    // Show debug info about selectors
                    const selectors = ['.notification-badge', '.badge.badge-primary', '.header-action-badge', '.nav-badge', '.badge'];
                    selectors.forEach(selector => {
                        const elements = document.querySelectorAll(selector);
                        if (elements.length > 0) {
                            log(`Found ${elements.length} elements with selector: ${selector}`);
                            elements.forEach((el, index) => {
                                const text = el.textContent.trim();
                                if (text) {
                                    log(`  Element ${index + 1}: "${text}"`);
                                }
                            });
                        }
                    });
                } else {
                    log('✗ Banner system failed to initialize');
                    document.getElementById('user-type').textContent = 'Unknown';
                }
            }, 1500);
        });
        
        // Test automatic banner after 3 seconds
        setTimeout(() => {
            if (window.notificationBanner) {
                log('Showing automatic welcome banner...');
                window.notificationBanner.showInfo('Welcome to the banner test system!');
            }
        }, 3000);
    </script>
</body>
</html>

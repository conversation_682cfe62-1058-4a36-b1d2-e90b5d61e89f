<?php
/**
 * LendSwift Installation Key Generator
 *
 * Standalone page to generate secure installation keys for LendSwift installer
 * This page must be accessed BEFORE running the installer
 *
 * @package LendSwift
 * @version 1.0
 */

/**
 * Generate unique installation key for this server
 */
function generateInstallationKey() {
    // Create unique server fingerprint
    $server_data = [
        $_SERVER['SERVER_NAME'] ?? 'localhost',
        $_SERVER['DOCUMENT_ROOT'] ?? '',
        php_uname('n'), // hostname
        php_uname('s'), // OS
        date('Y-m-d') // Date component for daily rotation
    ];

    $server_signature = implode('|', $server_data);
    $hash = hash('sha256', $server_signature . 'LENDSWIFT_SECURE_INSTALL_2025');

    // Return 20-character alphanumeric key
    return strtoupper(substr($hash, 0, 20));
}

/**
 * Save generated key to file for installer verification
 */
function saveKeyToFile($key) {
    $key_file = __DIR__ . '/.install_key';
    $key_data = [
        'key' => $key,
        'generated_at' => date('Y-m-d H:i:s'),
        'expires_at' => date('Y-m-d H:i:s', strtotime('+24 hours')),
        'server_name' => $_SERVER['SERVER_NAME'] ?? 'localhost'
    ];

    return file_put_contents($key_file, json_encode($key_data)) !== false;
}

// Handle key generation request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['generate_key'])) {
    $installation_key = generateInstallationKey();

    if (saveKeyToFile($installation_key)) {
        $success_message = "Installation key generated successfully!";
        $generated_key = $installation_key;
    } else {
        $error_message = "Failed to save installation key. Please check file permissions.";
    }
}

// Check if installer has already been run
$installer_completed = file_exists('../includes/core/.installed');
$key_file_exists = file_exists(__DIR__ . '/.install_key');

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LendSwift Security Key Generator</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            margin: 0;
        }
        
        .generator-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 600px;
            padding: 40px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #7f8c8d;
        }
        
        .key-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            margin: 20px 0;
            border-left: 4px solid #3498db;
        }
        
        .key-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .key-display {
            background: white;
            border: 2px dashed #6c757d;
            border-radius: 6px;
            padding: 15px;
            text-align: center;
            margin: 15px 0;
        }
        
        .key-value {
            font-family: 'Courier New', monospace;
            font-size: 18px;
            font-weight: bold;
            color: #e74c3c;
            letter-spacing: 1px;
            word-break: break-all;
            margin: 10px 0;
        }
        
        .btn {
            background: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background 0.3s ease;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        .btn-success {
            background: #27ae60;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .info-box {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            color: #0c5460;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            color: #856404;
        }
        
        .text-center {
            text-align: center;
        }
        
        .small {
            font-size: 12px;
            color: #6c757d;
        }
        
        .mt-20 {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="generator-container">
        <div class="header">
            <h1>🔐 LendSwift Installation Key Generator</h1>
            <p>Generate a secure installation key for your LendSwift deployment</p>
            <p><strong>Step 1:</strong> Generate key here → <strong>Step 2:</strong> Use key in installer</p>
        </div>

        <?php if ($installer_completed): ?>
            <div class="warning-box">
                <strong>⚠️ Installation Already Completed</strong><br>
                LendSwift has already been installed on this server. If you need to reinstall, please remove the installation marker file first.
            </div>
        <?php endif; ?>

        <?php if (isset($success_message)): ?>
            <div class="info-box" style="background: #d4edda; border-color: #27ae60; color: #155724;">
                <strong>✅ <?php echo htmlspecialchars($success_message); ?></strong>
            </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
            <div class="warning-box" style="background: #f8d7da; border-color: #e74c3c; color: #721c24;">
                <strong>❌ <?php echo htmlspecialchars($error_message); ?></strong>
            </div>
        <?php endif; ?>
        
        <div class="key-section">
            <h3>🎯 Generate Installation Key</h3>
            <p>Generate a unique installation key for this server. This key will be saved and required for the installer.</p>

            <?php if (isset($generated_key)): ?>
                <div class="key-display" style="border-color: #27ae60; background: #f8fff9;">
                    <div class="key-value" style="color: #27ae60;"><?php echo htmlspecialchars($generated_key); ?></div>
                    <div class="small">Generated: <?php echo date('Y-m-d H:i:s'); ?> | Valid for 24 hours</div>
                </div>

                <div class="text-center">
                    <button class="btn" onclick="copyToClipboard('<?php echo $generated_key; ?>')">📋 Copy Key</button>
                    <a href="install.php" class="btn btn-success">🚀 Go to Installer</a>
                </div>
            <?php elseif ($key_file_exists): ?>
                <?php
                $key_data = json_decode(file_get_contents(__DIR__ . '/.install_key'), true);
                $is_expired = strtotime($key_data['expires_at']) < time();
                ?>
                <div class="key-display" style="border-color: <?php echo $is_expired ? '#e74c3c' : '#3498db'; ?>;">
                    <div class="key-value" style="color: <?php echo $is_expired ? '#e74c3c' : '#3498db'; ?>;">
                        <?php echo $is_expired ? 'KEY EXPIRED' : htmlspecialchars($key_data['key']); ?>
                    </div>
                    <div class="small">
                        Generated: <?php echo $key_data['generated_at']; ?> |
                        <?php echo $is_expired ? 'EXPIRED' : 'Expires: ' . $key_data['expires_at']; ?>
                    </div>
                </div>

                <div class="text-center">
                    <?php if (!$is_expired): ?>
                        <button class="btn" onclick="copyToClipboard('<?php echo $key_data['key']; ?>')">📋 Copy Key</button>
                        <a href="install.php" class="btn btn-success">🚀 Go to Installer</a>
                    <?php endif; ?>
                    <form method="POST" style="display: inline;">
                        <button type="submit" name="generate_key" class="btn" style="background: #f39c12;">
                            🔄 Generate New Key
                        </button>
                    </form>
                </div>
            <?php else: ?>
                <div class="key-display">
                    <div class="key-value" style="color: #6c757d;">No key generated yet</div>
                    <div class="small">Click "Generate Key" to create your installation key</div>
                </div>

                <div class="text-center">
                    <form method="POST" style="display: inline;">
                        <button type="submit" name="generate_key" class="btn btn-success">
                            🔑 Generate Installation Key
                        </button>
                    </form>
                </div>
            <?php endif; ?>

            <div class="info-box">
                <strong>How it works:</strong> This key is uniquely generated for your server and saved to a file.
                The installer will verify this key to ensure secure installation. Keys expire after 24 hours for security.
            </div>
        </div>
        
        <div class="key-section">
            <h3>📋 Installation Instructions</h3>
            <ol style="text-align: left; margin: 20px 0; padding-left: 30px;">
                <li><strong>Generate Key:</strong> Click "Generate Installation Key" above</li>
                <li><strong>Copy Key:</strong> Copy the generated key to your clipboard</li>
                <li><strong>Run Installer:</strong> Go to the installer and enter the key when prompted</li>
                <li><strong>Complete Setup:</strong> Follow the installer steps to complete setup</li>
            </ol>

            <div class="warning-box">
                <strong>Important Notes:</strong>
                <ul style="text-align: left; margin: 10px 0; padding-left: 20px;">
                    <li>Keys are valid for 24 hours only</li>
                    <li>Each key is unique to this server</li>
                    <li>You must generate a key before running the installer</li>
                    <li>Keep the key secure and don't share it</li>
                </ul>
            </div>
        </div>
        
        <div class="info-box">
            <strong>🛡️ Security Information:</strong>
            <ul style="margin: 10px 0; padding-left: 20px;">
                <li>Keys are generated locally and never transmitted</li>
                <li>Server-specific keys are reproducible on the same server</li>
                <li>Random tokens are unique each time they're generated</li>
                <li>Use these keys during the LendSwift installation process</li>
            </ul>
        </div>
        
        <?php if (!$installer_completed): ?>
            <div class="text-center mt-20">
                <?php if (isset($generated_key) || ($key_file_exists && !$is_expired)): ?>
                    <a href="install.php" class="btn btn-success">🚀 Continue to Installer</a>
                <?php else: ?>
                    <p style="color: #6c757d;">Generate an installation key above to proceed</p>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>

    <script>
        function copyToClipboard(text) {
            if (text && text.trim()) {
                navigator.clipboard.writeText(text).then(function() {
                    alert('Key copied to clipboard!');
                }).catch(function(err) {
                    // Fallback for older browsers
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    alert('Key copied to clipboard!');
                });
            } else {
                alert('No key to copy!');
            }
        }
    </script>
</body>
</html>

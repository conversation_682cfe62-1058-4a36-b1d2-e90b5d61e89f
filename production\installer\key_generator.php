<?php
/**
 * LendSwift Security Key Generator
 * 
 * Standalone utility to generate installation security keys
 * 
 * @package LendSwift
 * @version 1.0
 */

/**
 * Generate security key based on server environment
 */
function generateSecurityKey() {
    $server_signature = $_SERVER['SERVER_NAME'] . $_SERVER['DOCUMENT_ROOT'] . php_uname();
    return substr(hash('sha256', $server_signature . 'LENDSWIFT_INSTALL'), 0, 16);
}

/**
 * Generate random installation token
 */
function generateRandomToken($length = 32) {
    return bin2hex(random_bytes($length));
}

// Handle AJAX requests
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');
    
    switch ($_POST['action']) {
        case 'generate_key':
            echo json_encode([
                'success' => true,
                'key' => generateSecurityKey(),
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            break;
            
        case 'generate_token':
            echo json_encode([
                'success' => true,
                'token' => generateRandomToken(),
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            break;
            
        default:
            echo json_encode([
                'success' => false,
                'error' => 'Invalid action'
            ]);
    }
    exit;
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LendSwift Security Key Generator</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            margin: 0;
        }
        
        .generator-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 600px;
            padding: 40px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #7f8c8d;
        }
        
        .key-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            margin: 20px 0;
            border-left: 4px solid #3498db;
        }
        
        .key-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .key-display {
            background: white;
            border: 2px dashed #6c757d;
            border-radius: 6px;
            padding: 15px;
            text-align: center;
            margin: 15px 0;
        }
        
        .key-value {
            font-family: 'Courier New', monospace;
            font-size: 18px;
            font-weight: bold;
            color: #e74c3c;
            letter-spacing: 1px;
            word-break: break-all;
            margin: 10px 0;
        }
        
        .btn {
            background: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background 0.3s ease;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        .btn-success {
            background: #27ae60;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .info-box {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            color: #0c5460;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            color: #856404;
        }
        
        .text-center {
            text-align: center;
        }
        
        .small {
            font-size: 12px;
            color: #6c757d;
        }
        
        .mt-20 {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="generator-container">
        <div class="header">
            <h1>🔐 LendSwift Security Key Generator</h1>
            <p>Generate secure installation keys for your LendSwift deployment</p>
        </div>
        
        <div class="key-section">
            <h3>🎯 Server-Specific Installation Key</h3>
            <p>This key is uniquely generated based on your server environment and is required for installation.</p>
            
            <div class="key-display">
                <div class="key-value" id="server-key">Click "Generate" to create your key</div>
                <div class="small">Generated: <span id="server-timestamp">-</span></div>
            </div>
            
            <div class="text-center">
                <button class="btn btn-success" onclick="generateServerKey()">🔄 Generate Server Key</button>
                <button class="btn" onclick="copyToClipboard('server-key')">📋 Copy Key</button>
            </div>
            
            <div class="info-box">
                <strong>How it works:</strong> This key is generated using your server's hostname, document root, and system information. 
                It ensures that only someone with access to this specific server can complete the installation.
            </div>
        </div>
        
        <div class="key-section">
            <h3>🎲 Random Installation Token</h3>
            <p>Alternative random token for additional security (optional).</p>
            
            <div class="key-display">
                <div class="key-value" id="random-token">Click "Generate" to create a random token</div>
                <div class="small">Generated: <span id="random-timestamp">-</span></div>
            </div>
            
            <div class="text-center">
                <button class="btn btn-success" onclick="generateRandomToken()">🎲 Generate Random Token</button>
                <button class="btn" onclick="copyToClipboard('random-token')">📋 Copy Token</button>
            </div>
            
            <div class="warning-box">
                <strong>Note:</strong> Random tokens are not tied to your server environment. 
                Use the server-specific key above for standard installations.
            </div>
        </div>
        
        <div class="info-box">
            <strong>🛡️ Security Information:</strong>
            <ul style="margin: 10px 0; padding-left: 20px;">
                <li>Keys are generated locally and never transmitted</li>
                <li>Server-specific keys are reproducible on the same server</li>
                <li>Random tokens are unique each time they're generated</li>
                <li>Use these keys during the LendSwift installation process</li>
            </ul>
        </div>
        
        <div class="text-center mt-20">
            <a href="install.php" class="btn">🚀 Go to Installer</a>
        </div>
    </div>

    <script>
        function generateServerKey() {
            fetch('key_generator.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=generate_key'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('server-key').textContent = data.key;
                    document.getElementById('server-timestamp').textContent = data.timestamp;
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                alert('Error: ' + error);
            });
        }
        
        function generateRandomToken() {
            fetch('key_generator.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=generate_token'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('random-token').textContent = data.token;
                    document.getElementById('random-timestamp').textContent = data.timestamp;
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                alert('Error: ' + error);
            });
        }
        
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            
            if (text && text !== 'Click "Generate" to create your key' && text !== 'Click "Generate" to create a random token') {
                navigator.clipboard.writeText(text).then(function() {
                    alert('Copied to clipboard!');
                }).catch(function(err) {
                    // Fallback for older browsers
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    alert('Copied to clipboard!');
                });
            } else {
                alert('Please generate a key first!');
            }
        }
        
        // Auto-generate server key on page load
        window.addEventListener('load', function() {
            generateServerKey();
        });
    </script>
</body>
</html>

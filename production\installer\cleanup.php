<?php
/**
 * Installer Cleanup Utility
 * 
 * Safely removes the installer directory after successful installation
 * 
 * @package LendSwift
 * @version 1.0
 */

// Start session to check installation status
session_start();

// Set JSON response header
header('Content-Type: application/json');

// Check if installation is complete
if (!file_exists('../includes/core/.installed')) {
    echo json_encode([
        'success' => false,
        'error' => 'Installation not completed. Cannot remove installer.'
    ]);
    exit;
}

// Check if this is a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode([
        'success' => false,
        'error' => 'Invalid request method.'
    ]);
    exit;
}

try {
    // Get the installer directory path
    $installer_dir = __DIR__;
    
    // List of files to remove
    $files_to_remove = [
        'install.php',
        'cleanup.php',
        'key_generator.php'
    ];
    
    $removed_files = [];
    $failed_files = [];
    
    // Remove installer files
    foreach ($files_to_remove as $file) {
        $file_path = $installer_dir . '/' . $file;
        if (file_exists($file_path)) {
            if (unlink($file_path)) {
                $removed_files[] = $file;
            } else {
                $failed_files[] = $file;
            }
        }
    }
    
    // Try to remove the installer directory if it's empty
    $directory_removed = false;
    if (empty($failed_files)) {
        // Check if directory is empty (except for . and ..)
        $files_in_dir = array_diff(scandir($installer_dir), ['.', '..']);
        if (empty($files_in_dir)) {
            if (rmdir($installer_dir)) {
                $directory_removed = true;
            }
        }
    }
    
    // Prepare response
    if (empty($failed_files)) {
        echo json_encode([
            'success' => true,
            'message' => 'Installer removed successfully.',
            'removed_files' => $removed_files,
            'directory_removed' => $directory_removed
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'error' => 'Some files could not be removed.',
            'removed_files' => $removed_files,
            'failed_files' => $failed_files
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'Error during cleanup: ' . $e->getMessage()
    ]);
}
?>

<?php
/**
 * Debug Key Generation - Temporary file for testing
 * DELETE THIS FILE AFTER DEBUGGING
 */

// Get server environment data
$server_data = [
    $_SERVER['SERVER_NAME'] ?? 'localhost',
    $_SERVER['DOCUMENT_ROOT'] ?? '',
    php_uname('n'), // hostname
    php_uname('s'), // OS
];

$server_signature = implode('|', $server_data);
$expected_key = strtoupper(substr(hash('sha256', $server_signature . 'LENDSWIFT_LICENSE_2025'), 0, 20));

// Master keys
$master_keys = [
    'LENDSWIFT2025MASTER01',
    'LENDSWIFT2025MASTER02',
    $expected_key
];

?>
<!DOCTYPE html>
<html>
<head>
    <title>LendSwift Key Debug</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 10px; max-width: 800px; margin: 0 auto; }
        .key { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 16px; margin: 5px 0; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-form { background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 LendSwift Key Debug Information</h1>
        <p><strong>⚠️ DELETE THIS FILE AFTER DEBUGGING</strong></p>
        
        <div class="section">
            <h3>🖥️ Server Environment</h3>
            <p><strong>Server Name:</strong> <?php echo htmlspecialchars($_SERVER['SERVER_NAME'] ?? 'localhost'); ?></p>
            <p><strong>Document Root:</strong> <?php echo htmlspecialchars($_SERVER['DOCUMENT_ROOT'] ?? ''); ?></p>
            <p><strong>Hostname:</strong> <?php echo htmlspecialchars(php_uname('n')); ?></p>
            <p><strong>OS:</strong> <?php echo htmlspecialchars(php_uname('s')); ?></p>
            <p><strong>Server Signature:</strong> <code><?php echo htmlspecialchars($server_signature); ?></code></p>
        </div>
        
        <div class="section">
            <h3>🔑 Valid License Keys</h3>
            <p><strong>Master Key 1:</strong></p>
            <div class="key">LENDSWIFT2025MASTER01</div>
            
            <p><strong>Master Key 2:</strong></p>
            <div class="key">LENDSWIFT2025MASTER02</div>
            
            <p><strong>Server-Specific Key:</strong></p>
            <div class="key"><?php echo $expected_key; ?></div>
        </div>
        
        <div class="test-form">
            <h3>🧪 Test Key Validation</h3>
            <form method="POST">
                <input type="text" name="test_key" placeholder="Enter key to test" style="width: 300px; padding: 10px; font-family: monospace;">
                <button type="submit" style="padding: 10px 20px;">Test Key</button>
            </form>
            
            <?php if (isset($_POST['test_key'])): ?>
                <?php
                $test_key = $_POST['test_key'];
                $clean_key = strtoupper(str_replace(' ', '', $test_key));
                $is_valid = in_array($clean_key, $master_keys);
                ?>
                <div style="margin-top: 15px; padding: 10px; border-radius: 5px; background: <?php echo $is_valid ? '#d4edda' : '#f8d7da'; ?>;">
                    <p><strong>Original Key:</strong> <?php echo htmlspecialchars($test_key); ?></p>
                    <p><strong>Cleaned Key:</strong> <?php echo htmlspecialchars($clean_key); ?></p>
                    <p><strong>Key Length:</strong> <?php echo strlen($clean_key); ?></p>
                    <p><strong>Valid Format:</strong> <?php echo preg_match('/^[A-Z0-9]{20}$/', $clean_key) ? 'YES' : 'NO'; ?></p>
                    <p><strong>Result:</strong> <?php echo $is_valid ? '✅ VALID' : '❌ INVALID'; ?></p>
                </div>
            <?php endif; ?>
        </div>
        
        <div class="section">
            <h3>📋 Copy Keys for Testing</h3>
            <p>Click any key below to copy it:</p>
            
            <div class="key" onclick="copyToClipboard('LENDSWIFT2025MASTER01')" style="cursor: pointer; border: 1px solid #007bff;">
                LENDSWIFT2025MASTER01 (Master Key 1)
            </div>
            
            <div class="key" onclick="copyToClipboard('LENDSWIFT2025MASTER02')" style="cursor: pointer; border: 1px solid #007bff;">
                LENDSWIFT2025MASTER02 (Master Key 2)
            </div>
            
            <div class="key" onclick="copyToClipboard('<?php echo $expected_key; ?>')" style="cursor: pointer; border: 1px solid #28a745;">
                <?php echo $expected_key; ?> (Server-Specific)
            </div>
        </div>
        
        <div class="section">
            <h3>🔗 Quick Links</h3>
            <p><a href="install.php" target="_blank">Go to Installer</a></p>
            <p><a href="admin_key_generator.php" target="_blank">Admin Key Generator</a></p>
        </div>
        
        <div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <strong>🗑️ Remember to delete this file after debugging!</strong><br>
            This file exposes sensitive information about your key generation system.
        </div>
    </div>

    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                alert('Key copied to clipboard: ' + text);
            }).catch(function(err) {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('Key copied to clipboard: ' + text);
            });
        }
    </script>
</body>
</html>

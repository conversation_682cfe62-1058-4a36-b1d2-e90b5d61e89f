# LendSwift Production Installer

A secure, comprehensive installer for the LendSwift Loan Management System production environment.

## 🚀 Quick Start

1. **Obtain License Key**
   - Contact your administrator or software vendor for a valid license key
   - License keys are 20-character alphanumeric codes

2. **Access the Installer**
   ```
   http://yourdomain.com/production/installer/install.php
   ```

3. **Super Admin Key Generation** (Admin Only)
   ```
   http://yourdomain.com/production/installer/admin_key_generator.php
   ```

## 📋 Installation Process

### Step 1: System Requirements
- Checks PHP version (>= 7.4)
- Verifies required PHP extensions
- Tests directory permissions
- Validates file system access

### Step 2: License Validation
- Validates license key provided by administrator
- Supports master keys and server-specific keys
- Ensures authorized installation only

### Step 3: Database Configuration
- Scans existing configuration
- Tests database connection
- Updates configuration file
- Creates database if needed

### Step 4: Database Installation
- Installs database schema
- Imports initial data
- Sets up required tables

### Step 5: System Verification
- File integrity check
- Database structure validation
- Permission verification
- PHP requirements confirmation

### Step 6: Installation Complete
- Displays admin credentials
- Provides access links
- Shows security recommendations
- Offers installer cleanup

## 🔐 Security Features

### Server-Specific Key Generation
The installer generates a unique security key based on:
- Server hostname
- Document root path
- System information
- Cryptographic hash (SHA-256)

### Installation Protection
- Prevents re-installation if already completed
- Session-based state management
- Secure form validation
- Automatic cleanup option

## 🛠️ Technical Requirements

### Server Requirements
- **PHP**: 7.4 or higher
- **Database**: MySQL 5.7+ or MariaDB 10.2+
- **Extensions**: PDO MySQL, mbstring, OpenSSL, cURL
- **Permissions**: Write access to uploads/ and includes/core/

### File Structure
```
production/installer/
├── install.php          # Main installer
├── cleanup.php          # Cleanup utility
├── key_generator.php    # Security key generator
└── README.md           # This file
```

## 📊 Default Credentials

After installation, use these credentials to access the admin panel:

- **URL**: `http://yourdomain.com/production/admin/`
- **Username**: `<EMAIL>`
- **Password**: `admin123`

⚠️ **IMPORTANT**: Change these credentials immediately after first login!

## 🔧 Configuration

### Database Settings
The installer will update these settings in `includes/core/config.php`:
- `DB_HOST` - Database server hostname
- `DB_USER` - Database username
- `DB_PASS` - Database password
- `DB_NAME` - Database name

### Production Settings
- Sets `DEVELOPMENT_MODE` to `false`
- Configures error reporting for production
- Optimizes security settings

## 🚨 Troubleshooting

### Common Issues

**1. Permission Denied Errors**
```bash
chmod 755 production/uploads/
chmod 755 production/uploads/documents/
chmod 755 production/uploads/receipts/
chmod 644 production/includes/core/config.php
```

**2. Database Connection Failed**
- Verify database credentials
- Ensure MySQL/MariaDB is running
- Check firewall settings
- Confirm database user permissions

**3. Missing PHP Extensions**
```bash
# Ubuntu/Debian
sudo apt-get install php-mysql php-mbstring php-curl php-openssl

# CentOS/RHEL
sudo yum install php-mysql php-mbstring php-curl php-openssl
```

**4. File Not Found Errors**
- Ensure all files are uploaded correctly
- Check file permissions
- Verify directory structure

### Error Logs
Check these locations for detailed error information:
- Server error logs
- PHP error logs
- Browser developer console

## 🛡️ Security Recommendations

### Post-Installation Security
1. **Change Default Credentials**
   - Update admin password immediately
   - Use strong, unique passwords

2. **Remove Installer**
   - Use the built-in cleanup function
   - Or manually delete the installer directory

3. **Configure HTTPS**
   - Set up SSL certificate
   - Force HTTPS redirects
   - Update BASE_URL if needed

4. **File Permissions**
   ```bash
   find production/ -type f -exec chmod 644 {} \;
   find production/ -type d -exec chmod 755 {} \;
   chmod 755 production/uploads/
   ```

5. **Database Security**
   - Use dedicated database user
   - Limit database permissions
   - Regular backups

### Ongoing Security
- Keep PHP and database updated
- Monitor access logs
- Regular security audits
- Backup strategy implementation

## 📞 Support

### Documentation
- Main documentation in `/production/README.md`
- Deployment checklist in `/production/DEPLOYMENT_CHECKLIST.md`
- Comprehensive analysis in `/COMPREHENSIVE_CODEBASE_ANALYSIS.md`

### System Health
After installation, monitor:
- Error logs for issues
- Database performance
- File upload functionality
- Email delivery system

## 🔄 Uninstallation

To remove LendSwift:
1. Delete all production files
2. Drop the database
3. Remove any cron jobs
4. Clear web server configuration

## 📝 Version Information

- **Installer Version**: 1.0
- **Compatible with**: LendSwift Production v1.0+
- **PHP Requirement**: 7.4+
- **Database**: MySQL 5.7+ / MariaDB 10.2+

---

**Note**: This installer is designed specifically for the LendSwift production environment with dynamic URL detection and enhanced security features.

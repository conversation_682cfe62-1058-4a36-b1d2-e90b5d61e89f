/**
 * Simplified Notification Banner System
 * Handles temporary visual alerts for new notifications
 */

class NotificationBanner {
    constructor() {
        this.banners = [];
        this.checkInterval = null;
        this.lastNotificationCount = 0;
        this.isAdmin = this.detectAdminMode();
        this.baseUrl = this.getBaseUrl();
        this.init();
    }

    detectAdminMode() {
        // Check multiple indicators for admin mode
        return document.body.classList.contains('admin-body') ||
               document.querySelector('.admin-container') !== null ||
               window.location.pathname.includes('/admin/');
    }

    getBaseUrl() {
        // Get base URL dynamically
        const path = window.location.pathname;
        if (path.includes('/admin/')) {
            return '../';
        } else if (path.includes('/tests/')) {
            return '../../';
        }
        return '';
    }

    init() {
        // Simple initialization without complex dependencies
        const initialCount = this.updateNotificationCount();

        // Only start periodic checking if we're on a dashboard page
        if (this.isDashboardPage()) {
            this.startPeriodicCheck();

            // Check for existing unread notifications after a short delay
            setTimeout(() => {
                // First check sidebar count, then AJAX if needed
                const sidebarCount = this.updateNotificationCount();
                if (sidebarCount > 0) {
                    console.debug(`Showing banner for ${sidebarCount} notifications from sidebar`);
                    this.showExistingNotificationBanner(sidebarCount);
                } else {
                    // Fallback to AJAX check
                    this.checkForExistingNotifications();
                }
            }, 2000);
        }

        // Listen for manual triggers
        document.addEventListener('showNotificationBanner', (e) => {
            this.handleManualTrigger(e.detail);
        });
    }

    isDashboardPage() {
        return document.querySelector('.dashboard-layout') !== null ||
               document.querySelector('.admin-container') !== null;
    }

    startPeriodicCheck() {
        // Check every 30 seconds for new notifications
        this.checkInterval = setInterval(() => {
            this.checkForNewNotifications();
        }, 30000);
    }

    stopPeriodicCheck() {
        if (this.checkInterval) {
            clearInterval(this.checkInterval);
            this.checkInterval = null;
        }
    }

    async checkForExistingNotifications() {
        try {
            const url = this.baseUrl + 'ajax/get-notification-count.php';
            const response = await fetch(url, {
                method: 'GET',
                credentials: 'same-origin'
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success && data.count > 0) {
                    // Show banner for existing unread notifications
                    this.showExistingNotificationBanner(data.count);
                    this.lastNotificationCount = data.count;
                    console.debug(`Found ${data.count} existing unread notifications`);
                } else if (data.success) {
                    this.lastNotificationCount = data.count;
                    console.debug('No unread notifications found');
                }
            }
        } catch (error) {
            console.debug('Initial notification check failed:', error.message);
        }
    }

    async checkForNewNotifications() {
        try {
            const url = this.baseUrl + 'ajax/get-notification-count.php';
            const response = await fetch(url, {
                method: 'GET',
                credentials: 'same-origin'
            });

            if (response.ok) {
                const data = await response.json();
                if (data.success && data.count > this.lastNotificationCount && this.lastNotificationCount > 0) {
                    const newNotifications = data.count - this.lastNotificationCount;
                    this.showNewNotificationBanner(newNotifications);
                    this.lastNotificationCount = data.count;
                    console.debug(`Found ${newNotifications} new notifications`);
                } else if (data.success) {
                    // Update count without showing banner
                    this.lastNotificationCount = data.count;
                }
            }
        } catch (error) {
            // Silently fail to avoid console spam
            console.debug('Notification check failed:', error.message);
        }
    }

    updateNotificationCount() {
        // Try multiple selectors to find notification count
        const selectors = [
            '.notification-badge',
            '.badge.badge-primary',
            '.header-action-badge',
            '.nav-badge',
            '.badge'
        ];

        let count = 0;
        for (const selector of selectors) {
            const badges = document.querySelectorAll(selector);
            for (const badge of badges) {
                if (badge && badge.textContent.trim()) {
                    const badgeCount = parseInt(badge.textContent) || 0;
                    if (badgeCount > 0) {
                        count = badgeCount;
                        console.debug(`Found notification count: ${count} using selector: ${selector}`);
                        break;
                    }
                }
            }
            if (count > 0) break;
        }

        this.lastNotificationCount = count;
        return count;
    }

    showExistingNotificationBanner(count = 1) {
        const message = count === 1
            ? 'You have 1 unread notification'
            : `You have ${count} unread notifications`;

        const bannerType = this.isAdmin ? 'admin' : 'user';
        const icon = this.isAdmin ? 'fas fa-exclamation-circle' : 'fas fa-bell';

        this.createBanner(message, bannerType, icon, 6000); // Show for 6 seconds
    }

    showNewNotificationBanner(count = 1) {
        const message = count === 1
            ? 'You have a new notification'
            : `You have ${count} new notifications`;

        const bannerType = this.isAdmin ? 'admin' : 'user';
        const icon = this.isAdmin ? 'fas fa-exclamation-circle' : 'fas fa-bell';

        this.createBanner(message, bannerType, icon);
    }

    createBanner(message, type = 'user', icon = 'fas fa-bell', duration = 5000) {
        // Create banner element
        const banner = document.createElement('div');
        banner.className = `notification-banner ${type}`;

        // Determine correct notification link based on context
        const notificationLink = this.isAdmin ? 'notifications.php' : '/?page=notifications';

        banner.innerHTML = `
            <i class="notification-banner-icon ${icon}"></i>
            <p class="notification-banner-message">${message}</p>
            <a href="${notificationLink}" class="notification-banner-action">View</a>
            <button class="notification-banner-close" aria-label="Close notification">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Add to page
        document.body.appendChild(banner);
        document.body.classList.add('has-notification-banner');
        
        // Add to tracking array
        this.banners.push(banner);

        // Show banner with animation
        setTimeout(() => {
            banner.classList.add('show', 'animate-in');
        }, 100);

        // Set up close button
        const closeBtn = banner.querySelector('.notification-banner-close');
        closeBtn.addEventListener('click', () => {
            this.closeBanner(banner);
        });

        // Auto-hide after duration
        setTimeout(() => {
            this.closeBanner(banner);
        }, duration);

        // Adjust positioning for multiple banners
        this.adjustBannerPositions();

        return banner;
    }

    closeBanner(banner) {
        if (!banner || !banner.parentNode) return;

        banner.classList.add('animate-out');
        banner.classList.remove('show');

        setTimeout(() => {
            if (banner.parentNode) {
                banner.parentNode.removeChild(banner);
            }
            
            // Remove from tracking array
            const index = this.banners.indexOf(banner);
            if (index > -1) {
                this.banners.splice(index, 1);
            }

            // Remove body class if no more banners
            if (this.banners.length === 0) {
                document.body.classList.remove('has-notification-banner');
            }

            // Readjust remaining banner positions
            this.adjustBannerPositions();
        }, 300);
    }

    adjustBannerPositions() {
        this.banners.forEach((banner, index) => {
            if (banner.parentNode) {
                banner.style.top = `${index * 60}px`;
            }
        });
    }

    // Public method to manually show a banner
    showBanner(message, type = 'user', icon = 'fas fa-bell', duration = 5000) {
        return this.createBanner(message, type, icon, duration);
    }

    // Public method to show success banner
    showSuccess(message, duration = 4000) {
        const type = this.isAdmin ? 'admin' : 'user';
        return this.createBanner(message, type, 'fas fa-check-circle', duration);
    }

    // Public method to show error banner
    showError(message, duration = 6000) {
        return this.createBanner(message, 'admin', 'fas fa-exclamation-triangle', duration);
    }

    // Public method to show info banner
    showInfo(message, duration = 5000) {
        const type = this.isAdmin ? 'admin' : 'user';
        return this.createBanner(message, type, 'fas fa-info-circle', duration);
    }

    // Handle manual triggers from other parts of the app
    handleManualTrigger(data) {
        if (data.type === 'new') {
            this.showNewNotificationBanner(data.count || 1);
        } else if (data.type === 'success') {
            this.showSuccess(data.message || 'Operation successful');
        } else if (data.type === 'error') {
            this.showError(data.message || 'An error occurred');
        } else if (data.type === 'info') {
            this.showInfo(data.message || 'Information');
        }
    }

    // Clean up when page is unloaded
    destroy() {
        this.stopPeriodicCheck();
        this.banners.forEach(banner => {
            if (banner.parentNode) {
                banner.parentNode.removeChild(banner);
            }
        });
        this.banners = [];
        document.body.classList.remove('has-notification-banner');
    }
}

// Initialize notification banner system when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize on dashboard pages (user or admin)
    if (document.querySelector('.dashboard-layout') || document.querySelector('.admin-container')) {
        try {
            window.notificationBanner = new NotificationBanner();
            console.debug('Notification banner system initialized');
        } catch (error) {
            console.error('Failed to initialize notification banner:', error);
        }
    }
});

// Clean up when page is unloaded
window.addEventListener('beforeunload', function() {
    if (window.notificationBanner) {
        window.notificationBanner.destroy();
    }
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NotificationBanner;
}

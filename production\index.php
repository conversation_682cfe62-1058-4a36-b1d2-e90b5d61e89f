<?php
/**
 * LendSwift - Professional Loan Management System
 *
 * Main entry point for the application.
 * This file handles routing and displays the appropriate page.
 *
 * @package LendSwift
 * @version 1.0.0
 * @copyright 2025 LendSwift. All rights reserved.
 * @license Commercial License
 */

// Include initialization file
require_once 'includes/init.php';

// Get the requested page from the URL
$page = $_GET['page'] ?? 'home';

// Sanitize the page parameter
$page = preg_replace('/[^a-zA-Z0-9_-]/', '', $page);

// Define allowed pages
$allowed_pages = [
    'home',
    'login',
    'register',
    'dashboard',
    'loan-application',
    'loan-details',
    'profile',
    'documents',
    'document-history',
    'calculator',
    'loan-calculator',
    'contact',
    'about',
    'terms',
    'privacy',
    'logout',
    'loans',
    'transactions',
    'transaction-details',
    'notifications',
    'support',
    'forgot-password',
    'reset-password',
    'services',
    'loan-products',
    'apply',
    'thank-you'
];

// Check if the requested page is allowed
if (!in_array($page, $allowed_pages)) {
    $page = 'home';
}

// Special handling for calculator page when user is logged in
if ($page === 'calculator' && is_user_logged_in()) {
    $page = 'loan-calculator';
}

// Check if the page file exists
$page_file = PAGES_PATH . '/' . $page . '.php';
if (!file_exists($page_file)) {
    // Fallback to home page if the requested page doesn't exist
    $page_file = PAGES_PATH . '/home.php';
    $page = 'home';
}

// Check if the user needs to be logged in for this page
$public_pages = ['home', 'login', 'register', 'contact', 'about', 'terms', 'privacy', 'services', 'loan-products', 'apply', 'thank-you'];
if (!in_array($page, $public_pages) && !is_user_logged_in()) {
    // Redirect to login page if the user is not logged in
    set_flash_message('error', 'Please log in to access this page.');
    redirect(BASE_URL . '?page=login');
}

// Handle logout
if ($page === 'logout' && is_user_logged_in()) {
    // Destroy the session
    session_destroy();
    // Redirect to home page
    redirect(BASE_URL);
}

// Start output buffering
ob_start();

// Define pages that should not include header and footer
$standalone_pages = ['login', 'forgot-password', 'reset-password', 'dashboard', 'profile', 'documents', 'document-history', 'loan-application', 'loan-details', 'loans', 'transactions', 'transaction-details', 'notifications', 'calculator', 'loan-calculator', 'support'];

if (in_array($page, $standalone_pages)) {
    // Include only the page content for standalone pages
    include $page_file;
} else {
    // Include header, page content, and footer for regular pages
    include INCLUDES_PATH . '/header.php';
    include $page_file;
    include INCLUDES_PATH . '/footer.php';
}

// Send the output to the browser
ob_end_flush();
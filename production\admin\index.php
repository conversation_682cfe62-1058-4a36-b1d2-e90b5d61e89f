<?php
/**
 * LendSwift - Admin Dashboard
 *
 * This file contains the main dashboard for administrators.
 *
 * @package LendSwift
 * @version 1.0.0
 * @copyright 2025 LendSwift. All rights reserved.
 * @license Commercial License
 */

// Define LENDSWIFT constant to prevent direct access to included files
if (!defined('LENDSWIFT')) {
    define('LENDSWIFT', true);
}

// Include initialization file
require_once '../includes/init.php';

// Check if the admin is logged in
if (!is_admin_logged_in()) {
    set_flash_message('error', 'Please log in to access the admin dashboard.');
    redirect('login.php');
}

// Get admin information
$admin_id = get_current_admin_id();
$admin_name = $_SESSION['admin_name'] ?? 'Administrator';

// Get database connection
$db = getDbConnection();

// Get counts for dashboard widgets
try {
    // Total users
    $result = $db->query("SELECT COUNT(*) as count FROM users");
    $row = $result->fetch_assoc();
    $total_users = $row['count'];

    // Total loan applications
    $result = $db->query("SELECT COUNT(*) as count FROM loan_applications");
    $row = $result->fetch_assoc();
    $total_applications = $row['count'];

    // Pending applications
    $result = $db->query("SELECT COUNT(*) as count FROM loan_applications WHERE status_id = 1"); // Assuming status_id 1 is 'Pending Review'
    $row = $result->fetch_assoc();
    $pending_applications = $row['count'];

    // Approved applications
    $result = $db->query("SELECT COUNT(*) as count FROM loan_applications WHERE status_id = 3"); // Assuming status_id 3 is 'Approved'
    $row = $result->fetch_assoc();
    $approved_applications = $row['count'];

    // Get application counts by status for chart
    $application_stats = [];
    $result = $db->query("
        SELECT ls.name as status, COUNT(*) as count
        FROM loan_applications la
        JOIN loan_statuses ls ON la.status_id = ls.id
        GROUP BY la.status_id
        ORDER BY count DESC
    ");

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $application_stats[] = $row;
        }
    }

    // Recent applications
    $recent_applications = [];
    $result = $db->query("
        SELECT la.id, u.name as user_name, la.applied_amount, c.code as currency_code,
               ls.name as status, la.submission_date
        FROM loan_applications la
        JOIN users u ON la.user_id = u.id
        JOIN currencies c ON la.currency_id = c.id
        JOIN loan_statuses ls ON la.status_id = ls.id
        ORDER BY la.submission_date DESC
        LIMIT 5
    ");

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $recent_applications[] = $row;
        }
    }
} catch (Exception $e) {
    // Log the error
    error_log('Admin Dashboard Error: ' . $e->getMessage());

    // Set default values
    $total_users = 0;
    $total_applications = 0;
    $pending_applications = 0;
    $approved_applications = 0;
    $application_stats = [];
    $recent_applications = [];
}

// Include admin header
include '../includes/admin_header.php';
?>

<div class="admin-dashboard">
    <div class="dashboard-header">
        <h1>Dashboard</h1>
        <p>Welcome back, <?php echo htmlspecialchars($admin_name); ?>!</p>
    </div>

    <div class="dashboard-stats">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-content">
                <h3>Total Users</h3>
                <p class="stat-number"><?php echo $total_users; ?></p>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-file-alt"></i>
            </div>
            <div class="stat-content">
                <h3>Total Applications</h3>
                <p class="stat-number"><?php echo $total_applications; ?></p>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stat-content">
                <h3>Pending Applications</h3>
                <p class="stat-number"><?php echo $pending_applications; ?></p>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stat-content">
                <h3>Approved Applications</h3>
                <p class="stat-number"><?php echo $approved_applications; ?></p>
            </div>
        </div>
    </div>

    <div class="dashboard-row">
        <div class="dashboard-card">
            <div class="card-header">
                <h2>Recent Loan Applications</h2>
                <a href="applications.php" class="button button-small">View All</a>
            </div>

            <div class="card-content">
                <?php if (empty($recent_applications)): ?>
                    <p>No recent applications found.</p>
                <?php else: ?>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Applicant</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_applications as $application): ?>
                                <tr>
                                    <td><?php echo $application['id']; ?></td>
                                    <td><?php echo htmlspecialchars($application['user_name']); ?></td>
                                    <td><?php echo $application['currency_code'] . ' ' . number_format($application['applied_amount'], 2); ?></td>
                                    <td>
                                        <span class="status-badge status-<?php echo strtolower(str_replace(' ', '-', $application['status'])); ?>">
                                            <?php echo htmlspecialchars($application['status']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo date('M d, Y', strtotime($application['submission_date'])); ?></td>
                                    <td>
                                        <a href="application-details.php?id=<?php echo $application['id']; ?>" class="button button-small">View</a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>

        <div class="dashboard-card">
            <div class="card-header">
                <h2>Quick Actions</h2>
            </div>
            <div class="card-content">
                <ul class="quick-actions-list">
                    <li>
                        <a href="users.php">
                            <i class="fas fa-user-plus"></i>
                            <span>Manage Users</span>
                        </a>
                    </li>
                    <li>
                        <a href="applications.php">
                            <i class="fas fa-tasks"></i>
                            <span>Review Applications</span>
                        </a>
                    </li>
                    <li>
                        <a href="loan-products.php">
                            <i class="fas fa-money-bill-wave"></i>
                            <span>Loan Products</span>
                        </a>
                    </li>
                    <li>
                        <a href="transactions.php">
                            <i class="fas fa-exchange-alt"></i>
                            <span>Transactions</span>
                        </a>
                    </li>
                    <li>
                        <a href="support.php">
                            <i class="fas fa-headset"></i>
                            <span>Support Tickets</span>
                        </a>
                    </li>
                    <li>
                        <a href="settings.php">
                            <i class="fas fa-cog"></i>
                            <span>System Settings</span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <div class="dashboard-row">
        <div class="dashboard-card">
            <div class="card-header">
                <h2>Applications by Status</h2>
            </div>
            <div class="card-content">
                <?php if (empty($application_stats)): ?>
                    <p>No application data available.</p>
                <?php else: ?>
                    <div class="chart-container">
                        <div class="bar-chart">
                            <?php
                            // Find the maximum count for scaling
                            $max_count = 0;
                            foreach ($application_stats as $stat) {
                                if ($stat['count'] > $max_count) {
                                    $max_count = $stat['count'];
                                }
                            }

                            foreach ($application_stats as $stat):
                                // Calculate percentage for bar width
                                $percentage = ($max_count > 0) ? ($stat['count'] / $max_count * 100) : 0;

                                // Generate a CSS class based on status name
                                $status_class = 'status-' . strtolower(str_replace(' ', '-', $stat['status']));
                            ?>
                            <div class="chart-item">
                                <div class="chart-label"><?php echo htmlspecialchars($stat['status']); ?></div>
                                <div class="chart-bar-container">
                                    <div class="chart-bar <?php echo $status_class; ?>" style="width: <?php echo $percentage; ?>%;">
                                        <span class="chart-value"><?php echo $stat['count']; ?></span>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php
// Include admin footer
include '../includes/admin_footer.php';
?>

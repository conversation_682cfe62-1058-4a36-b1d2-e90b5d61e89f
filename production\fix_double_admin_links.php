<?php
/**
 * Fix Double Admin Links
 *
 * This script fixes the double admin/admin/ issue by replacing
 * BASE_URL . '/admin/' patterns with relative paths in admin files
 */

// Define LENDSWIFT constant to prevent direct access error
define('LENDSWIFT', true);

// Include the configuration
require_once 'includes/core/config.php';

/**
 * Fix admin links in a file
 */
function fixAdminLinksInFile($file_path) {
    if (!file_exists($file_path)) {
        return ['success' => false, 'message' => 'File not found: ' . $file_path];
    }
    
    $content = file_get_contents($file_path);
    if ($content === false) {
        return ['success' => false, 'message' => 'Could not read file: ' . $file_path];
    }
    
    $original_content = $content;
    $changes = 0;
    
    // Patterns to fix
    $patterns = [
        // Form actions
        '/action="<\?php echo BASE_URL; \?>\/admin\/([^"]+)"/' => 'action="$1"',
        
        // Href links
        '/href="<\?php echo BASE_URL; \?>\/admin\/([^"]+)"/' => 'href="$1"',
        
        // Redirect calls
        '/redirect\(BASE_URL \. \'\/admin\/([^\']+)\'\)/' => 'redirect(\'$1\')',
        
        // JavaScript/AJAX URLs
        '/BASE_URL \. \'\/admin\/([^\']+)\'/' => '\'$1\'',
        
        // Double quotes version
        '/BASE_URL \. "\/admin\/([^"]+)"/' => '"$1"',
    ];
    
    foreach ($patterns as $pattern => $replacement) {
        $new_content = preg_replace($pattern, $replacement, $content);
        if ($new_content !== $content) {
            $changes += preg_match_all($pattern, $content, $matches);
            $content = $new_content;
        }
    }
    
    if ($content !== $original_content) {
        // Create backup
        $backup_file = $file_path . '.backup.' . date('Y-m-d-H-i-s');
        file_put_contents($backup_file, $original_content);
        
        // Write updated content
        if (file_put_contents($file_path, $content) !== false) {
            return [
                'success' => true, 
                'message' => "Fixed $changes admin links in " . basename($file_path),
                'changes' => $changes,
                'backup' => $backup_file
            ];
        } else {
            return ['success' => false, 'message' => 'Could not write to file: ' . $file_path];
        }
    } else {
        return [
            'success' => true, 
            'message' => 'No admin links to fix in ' . basename($file_path),
            'changes' => 0
        ];
    }
}

/**
 * Get all PHP files in admin directory
 */
function getAdminPhpFiles() {
    $admin_dir = 'admin';
    $files = [];
    
    if (is_dir($admin_dir)) {
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($admin_dir)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'php') {
                $files[] = $file->getPathname();
            }
        }
    }
    
    return $files;
}

// Main execution
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $results = [];
    $total_changes = 0;
    
    if (isset($_POST['fix_all_admin_files'])) {
        $admin_files = getAdminPhpFiles();
        
        foreach ($admin_files as $file) {
            $result = fixAdminLinksInFile($file);
            $results[] = $result;
            if ($result['success'] && isset($result['changes'])) {
                $total_changes += $result['changes'];
            }
        }
    } elseif (isset($_POST['fix_single_file']) && !empty($_POST['file_path'])) {
        $file_path = $_POST['file_path'];
        $result = fixAdminLinksInFile($file_path);
        $results[] = $result;
        if ($result['success'] && isset($result['changes'])) {
            $total_changes += $result['changes'];
        }
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Double Admin Links - LendSwift Production</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 800px; margin: 0 auto; }
        .result { background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        h1 { color: #333; }
        .button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .button:hover { background: #0056b3; }
        .button-danger { background: #dc3545; }
        .button-danger:hover { background: #c82333; }
        code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
        .file-list { max-height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Fix Double Admin Links</h1>
        
        <div class="result info">
            <h3>📋 Current Configuration</h3>
            <p><strong>BASE_URL:</strong> <code><?php echo BASE_URL; ?></code></p>
            <p><strong>Problem:</strong> Links using <code>BASE_URL . '/admin/'</code> create double admin paths</p>
            <p><strong>Solution:</strong> Replace with relative paths like <code>href="users.php"</code></p>
        </div>

        <?php if (!empty($results)): ?>
            <div class="result <?php echo $total_changes > 0 ? 'success' : 'warning'; ?>">
                <h3>🔧 Fix Results</h3>
                <p><strong>Total Changes Made:</strong> <?php echo $total_changes; ?></p>
                
                <?php foreach ($results as $result): ?>
                    <div class="result <?php echo $result['success'] ? 'success' : 'error'; ?>">
                        <?php echo $result['message']; ?>
                        <?php if (isset($result['backup'])): ?>
                            <br><small>Backup: <?php echo basename($result['backup']); ?></small>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <div class="result">
            <h3>🚀 Fix Options</h3>
            
            <form method="POST" style="margin: 10px 0;">
                <button type="submit" name="fix_all_admin_files" class="button">
                    Fix All Admin Files
                </button>
                <p><small>This will fix all PHP files in the admin directory</small></p>
            </form>
            
            <form method="POST" style="margin: 10px 0;">
                <input type="text" name="file_path" placeholder="admin/specific-file.php" style="padding: 8px; width: 300px;">
                <button type="submit" name="fix_single_file" class="button">
                    Fix Single File
                </button>
                <p><small>Fix a specific file by entering its path</small></p>
            </form>
        </div>

        <div class="result info">
            <h3>📁 Admin Files Found</h3>
            <div class="file-list">
                <?php 
                $admin_files = getAdminPhpFiles();
                foreach ($admin_files as $file): 
                ?>
                    <div><?php echo htmlspecialchars($file); ?></div>
                <?php endforeach; ?>
            </div>
            <p><strong>Total Files:</strong> <?php echo count($admin_files); ?></p>
        </div>
        
        <div class="result">
            <h3>🧪 Test Links</h3>
            <p><a href="test_admin_links.php" target="_blank">Run Admin Links Test</a></p>
            <p><a href="admin/add-user.php" target="_blank">Test Add User Page</a></p>
            <p><a href="test_url_detection.php" target="_blank">Test URL Detection</a></p>
        </div>
    </div>
</body>
</html>

<?php
/**
 * Email Functions
 *
 * This file contains functions for sending emails.
 *
 * @package LendSwift
 */

// Prevent direct access
if (!defined('LENDSWIFT')) {
    die('Direct access to this file is not allowed.');
}

/**
 * Send a notification email with template
 *
 * @param string $to Recipient email address
 * @param string $subject Email subject
 * @param string $message Email message (HTML)
 * @param string $from_email From email address (optional)
 * @param string $from_name From name (optional)
 * @param array $attachments Array of attachment file paths (optional)
 * @return bool True if email was sent, false otherwise
 */
function send_notification_email($to, $subject, $message, $from_email = '', $from_name = '', $attachments = []) {
    // Get SMTP settings from database
    $db = getDbConnection();
    $result = $db->query("SELECT * FROM settings WHERE setting_key LIKE 'smtp_%'");

    $smtp_settings = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $key = str_replace('smtp_', '', $row['setting_key']);
            $smtp_settings[$key] = $row['setting_value'];
        }
    }

    // Get dynamic company/site name from settings
    $company_settings = [];
    $company_result = $db->query("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('company_name', 'site_name', 'email_from_name')");
    if ($company_result && $company_result->num_rows > 0) {
        while ($row = $company_result->fetch_assoc()) {
            $company_settings[$row['setting_key']] = $row['setting_value'];
        }
    }

    // Set default from email and name if not provided
    if (empty($from_email)) {
        $from_email = $smtp_settings['username'] ?? '<EMAIL>';
    }

    if (empty($from_name)) {
        // Use email_from_name first, then company_name, then site_name as fallback
        $from_name = $company_settings['email_from_name'] ??
                    $company_settings['company_name'] ??
                    $company_settings['site_name'] ??
                    'LendSwift';
    }

    // Get site logo for email template - use dynamic logo from database
    $site_logo = get_setting('site_logo', '/assets/images/logo.svg');
    $logo_url = BASE_URL . $site_logo;
    $site_name = $company_settings['site_name'] ??
                $company_settings['company_name'] ??
                'LendSwift';

    // Check if logo file exists, if not use fallback
    $logo_exists = !empty($site_logo) && file_exists(BASE_PATH . $site_logo);

    // Wrap message in email template
    $email_body = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>' . htmlspecialchars($subject) . '</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                margin: 0;
                padding: 0;
            }
            .email-container {
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
            }
            .email-header {
                text-align: center;
                padding-bottom: 20px;
                border-bottom: 1px solid #eee;
            }
            .email-logo {
                max-height: 60px;
                max-width: 200px;
            }
            .email-content {
                padding: 20px 0;
            }
            .email-footer {
                padding-top: 20px;
                border-top: 1px solid #eee;
                font-size: 12px;
                color: #777;
                text-align: center;
            }
            @media only screen and (max-width: 600px) {
                .email-container {
                    width: 100% !important;
                }
            }
        </style>
    </head>
    <body>
        <div class="email-container">
            <div class="email-header">';

    // Add logo or company name fallback
    if ($logo_exists) {
        $email_body .= '
                <img src="' . $logo_url . '" alt="' . htmlspecialchars($site_name) . '" class="email-logo">';
    } else {
        $email_body .= '
                <div class="email-logo-text" style="font-size: 24px; font-weight: bold; color: #4f46e5; margin: 10px 0;">' . htmlspecialchars($site_name) . '</div>';
    }

    $email_body .= '
            </div>
            <div class="email-content">
                ' . $message . '
            </div>
            <div class="email-footer">
                <p>&copy; ' . date('Y') . ' ' . htmlspecialchars($site_name) . '. All rights reserved.</p>
                <p>This is an automated email, please do not reply.</p>
            </div>
        </div>
    </body>
    </html>
    ';

    // Use PHPMailer if available, otherwise use PHP mail function
    if (class_exists('PHPMailer\\PHPMailer\\PHPMailer')) {
        require_once dirname(__FILE__) . '/../vendor/autoload.php';

        $mail = new PHPMailer\PHPMailer\PHPMailer(true);

        try {
            // Server settings
            $mail->isSMTP();
            $mail->Host = $smtp_settings['host'] ?? 'smtp.hostinger.com';
            $mail->SMTPAuth = true;
            $mail->Username = $smtp_settings['username'] ?? '<EMAIL>';
            $mail->Password = $smtp_settings['password'] ?? 'Money2025@Demo#';
            $mail->SMTPSecure = $smtp_settings['encryption'] ?? 'ssl';
            $mail->Port = $smtp_settings['port'] ?? 465;

            // Recipients
            $mail->setFrom($from_email, $from_name);
            $mail->addAddress($to);

            // Content
            $mail->isHTML(true);
            $mail->Subject = $subject;
            $mail->Body = $email_body;
            $mail->AltBody = strip_tags(str_replace('<br>', "\n", $message));

            // Attachments
            if (!empty($attachments)) {
                foreach ($attachments as $attachment) {
                    if (file_exists($attachment)) {
                        $mail->addAttachment($attachment);
                    }
                }
            }

            $mail->send();
            return true;
        } catch (Exception $e) {
            error_log('Email Error: ' . $mail->ErrorInfo);
            return false;
        }
    } else {
        // Fallback to PHP mail function
        $headers = "MIME-Version: 1.0" . "\r\n";
        $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
        $headers .= "From: " . $from_name . " <" . $from_email . ">" . "\r\n";

        return mail($to, $subject, $email_body, $headers);
    }
}

/**
 * Send application status update email
 *
 * @param int $application_id Application ID
 * @param int $status_id New status ID
 * @param string $admin_note Admin note (optional)
 * @return bool True if email was sent, false otherwise
 */
function send_application_status_email($application_id, $status_id, $admin_note = '') {
    $db = getDbConnection();

    // Get application details
    $stmt = $db->prepare("
        SELECT
            la.*,
            u.name AS user_name,
            u.email AS user_email,
            lp.name AS product_name,
            ls.name AS status_name,
            c.symbol AS currency_symbol
        FROM
            loan_applications la
        JOIN
            users u ON la.user_id = u.id
        JOIN
            loan_products lp ON la.loan_product_id = lp.id
        JOIN
            loan_statuses ls ON la.status_id = ls.id
        JOIN
            currencies c ON la.currency_id = c.id
        WHERE
            la.id = ?
    ");

    $stmt->bind_param("i", $application_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if (!$result || $result->num_rows === 0) {
        return false;
    }

    $application = $result->fetch_assoc();

    // Get status name
    $stmt = $db->prepare("SELECT name FROM loan_statuses WHERE id = ?");
    $stmt->bind_param("i", $status_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if (!$result || $result->num_rows === 0) {
        return false;
    }

    $status = $result->fetch_assoc();

    // Prepare email content
    $subject = "Loan Application Status Update - #" . $application_id;

    $message = "
    <p>Dear " . htmlspecialchars($application['user_name']) . ",</p>

    <p>Your loan application #" . $application_id . " for " . htmlspecialchars($application['product_name']) . " has been updated.</p>

    <p><strong>New Status:</strong> " . htmlspecialchars($status['name']) . "</p>
    <p><strong>Amount:</strong> " . $application['currency_symbol'] . number_format($application['applied_amount'], 2) . "</p>
    <p><strong>Submission Date:</strong> " . date('F j, Y', strtotime($application['submission_date'])) . "</p>
    ";

    if (!empty($admin_note)) {
        $message .= "<p><strong>Note from Administrator:</strong> " . nl2br(htmlspecialchars($admin_note)) . "</p>";
    }

    $message .= "
    <p>You can log in to your account to view more details about your application.</p>

    <p>Thank you for choosing our services.</p>

    <p>Best regards,<br>
    The {$site_name} Team</p>
    ";

    // Send email
    return send_notification_email($application['user_email'], $subject, $message);
}

/**
 * Send document request email
 *
 * @param int $application_id Application ID
 * @param string $document_name Document name
 * @param string $document_description Document description
 * @return bool True if email was sent, false otherwise
 */
function send_document_request_email($application_id, $document_name, $document_description) {
    $db = getDbConnection();

    // Get application details
    $stmt = $db->prepare("
        SELECT
            la.*,
            u.name AS user_name,
            u.email AS user_email,
            lp.name AS product_name
        FROM
            loan_applications la
        JOIN
            users u ON la.user_id = u.id
        JOIN
            loan_products lp ON la.loan_product_id = lp.id
        WHERE
            la.id = ?
    ");

    $stmt->bind_param("i", $application_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if (!$result || $result->num_rows === 0) {
        return false;
    }

    $application = $result->fetch_assoc();

    // Prepare email content
    $subject = "Document Request for Loan Application #" . $application_id;

    $message = "
    <p>Dear " . htmlspecialchars($application['user_name']) . ",</p>

    <p>We need additional documentation for your loan application #" . $application_id . " for " . htmlspecialchars($application['product_name']) . ".</p>

    <p><strong>Document Requested:</strong> " . htmlspecialchars($document_name) . "</p>
    ";

    if (!empty($document_description)) {
        $message .= "<p><strong>Description:</strong> " . nl2br(htmlspecialchars($document_description)) . "</p>";
    }

    $message .= "
    <p>Please log in to your account and upload the requested document as soon as possible to avoid delays in processing your application.</p>

    <p>Thank you for your cooperation.</p>

    <p>Best regards,<br>
    The {$site_name} Team</p>
    ";

    // Send email
    return send_notification_email($application['user_email'], $subject, $message);
}

/**
 * Send welcome email to new user
 *
 * @param int $user_id User ID
 * @param string $password Plain text password (optional)
 * @return bool True if email was sent, false otherwise
 */
function send_welcome_email($user_id, $password = '') {
    $db = getDbConnection();

    // Get user details
    $stmt = $db->prepare("
        SELECT
            u.*,
            c.name AS currency_name,
            c.code AS currency_code,
            c.symbol AS currency_symbol
        FROM
            users u
        LEFT JOIN
            currencies c ON u.currency_id = c.id
        WHERE
            u.id = ?
    ");

    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if (!$result || $result->num_rows === 0) {
        return false;
    }

    $user = $result->fetch_assoc();

    // Get dynamic company/site name from settings
    $company_settings = [];
    $company_result = $db->query("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('company_name', 'site_name', 'email_from_name')");
    if ($company_result && $company_result->num_rows > 0) {
        while ($row = $company_result->fetch_assoc()) {
            $company_settings[$row['setting_key']] = $row['setting_value'];
        }
    }

    // Get welcome email template
    $stmt = $db->prepare("SELECT * FROM email_templates WHERE name = 'Welcome Email'");
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        $template = $result->fetch_assoc();
        $subject = $template['subject'];
        $message = $template['content'];
    } else {
        // Default template if not found in database - use dynamic company name
        $default_company_name = $company_settings['company_name'] ?? $company_settings['site_name'] ?? 'LendSwift Financial';
        $subject = "Welcome to {$default_company_name}";
        $message = "<p>Dear {user_name},</p><p>Welcome to {$default_company_name}! We're excited to have you on board.</p><p>Your account has been created successfully. You can now log in to your account and apply for loans.</p><p>If you have any questions, please don't hesitate to contact us.</p><p>Best regards,<br>The {$default_company_name} Team</p>";
    }

    // Get site settings from database
    $site_settings = [];
    $settings_result = $db->query("SELECT * FROM settings WHERE setting_key IN ('site_name', 'site_url')");

    if ($settings_result && $settings_result->num_rows > 0) {
        while ($row = $settings_result->fetch_assoc()) {
            $site_settings[$row['setting_key']] = $row['setting_value'];
        }
    }

    $site_name = $company_settings['site_name'] ?? $company_settings['company_name'] ?? 'LendSwift';
    $site_url = $site_settings['site_url'] ?? BASE_URL;

    // Always use a relative path for the login URL to ensure it works regardless of domain
    $login_url = '/pages/login.php';

    // If we're using a subdirectory installation (like pfloans.com), include it in the path
    if (strpos($_SERVER['REQUEST_URI'] ?? '', '/pfloans.com/') !== false) {
        $login_url = '/pfloans.com' . $login_url;
    }

    // Replace placeholders
    $subject = str_replace('{site_name}', $site_name, $subject);
    $subject = str_replace('{user_name}', $user['name'], $subject);

    $message = str_replace('{site_name}', $site_name, $message);
    $message = str_replace('{user_name}', $user['name'], $message);
    $message = str_replace('{user_email}', $user['email'], $message);
    $message = str_replace('{login_url}', $login_url, $message);

    // Add user details card
    $user_details = '
    <div style="background-color: #f9f9f9; border-radius: 5px; padding: 15px; margin: 20px 0;">
        <h3 style="color: #4f46e5; margin-top: 0;">Your Account Details</h3>
        <table style="width: 100%; border-collapse: collapse;">
            <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #eee; width: 40%;"><strong>Name:</strong></td>
                <td style="padding: 8px 0; border-bottom: 1px solid #eee;">' . htmlspecialchars($user['name']) . '</td>
            </tr>
            <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Email:</strong></td>
                <td style="padding: 8px 0; border-bottom: 1px solid #eee;">' . htmlspecialchars($user['email']) . '</td>
            </tr>
    ';

    if (!empty($password)) {
        $user_details .= '
            <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Password:</strong></td>
                <td style="padding: 8px 0; border-bottom: 1px solid #eee;">' . $password . '</td>
            </tr>
        ';
    }

    if (!empty($user['phone'])) {
        $user_details .= '
            <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Phone:</strong></td>
                <td style="padding: 8px 0; border-bottom: 1px solid #eee;">' . htmlspecialchars($user['phone']) . '</td>
            </tr>
        ';
    }

    if (!empty($user['address'])) {
        $user_details .= '
            <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Address:</strong></td>
                <td style="padding: 8px 0; border-bottom: 1px solid #eee;">' . htmlspecialchars($user['address']) . '</td>
            </tr>
        ';
    }

    if (!empty($user['currency_name'])) {
        $user_details .= '
            <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Preferred Currency:</strong></td>
                <td style="padding: 8px 0; border-bottom: 1px solid #eee;">' . $user['currency_symbol'] . ' ' . htmlspecialchars($user['currency_code']) . ' - ' . htmlspecialchars($user['currency_name']) . '</td>
            </tr>
        ';
    }

    $user_details .= '
            <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Status:</strong></td>
                <td style="padding: 8px 0; border-bottom: 1px solid #eee;">' . ucfirst(htmlspecialchars($user['status'])) . '</td>
            </tr>
            <tr>
                <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>Created On:</strong></td>
                <td style="padding: 8px 0; border-bottom: 1px solid #eee;">' . date('F j, Y', strtotime($user['created_at'])) . '</td>
            </tr>
        </table>
        <div style="margin-top: 15px;">
            <a href="' . $site_url . $login_url . '" style="display: inline-block; background-color: #4f46e5; color: white; text-decoration: none; padding: 10px 20px; border-radius: 5px;">Login to Your Account</a>
        </div>
    </div>
    ';

    // Insert user details card after the first paragraph
    $message = preg_replace('/<\/p>/', '</p>' . $user_details, $message, 1);

    // Send email
    return send_notification_email($user['email'], $subject, $message);
}

/**
 * Send user status change notification email
 *
 * @param int $user_id User ID
 * @param string $new_status New status (active, inactive, suspended)
 * @param string $admin_note Admin note (optional)
 * @return bool True if email was sent, false otherwise
 */
function send_user_status_email($user_id, $new_status, $admin_note = '') {
    $db = getDbConnection();

    // Get user details
    $stmt = $db->prepare("
        SELECT
            u.*,
            c.name AS currency_name,
            c.code AS currency_code,
            c.symbol AS currency_symbol
        FROM
            users u
        LEFT JOIN
            currencies c ON u.currency_id = c.id
        WHERE
            u.id = ?
    ");

    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if (!$result || $result->num_rows === 0) {
        return false;
    }

    $user = $result->fetch_assoc();

    // Get site settings from database
    $site_settings = [];
    $settings_result = $db->query("SELECT * FROM settings WHERE setting_key IN ('site_name', 'site_url')");

    if ($settings_result && $settings_result->num_rows > 0) {
        while ($row = $settings_result->fetch_assoc()) {
            $site_settings[$row['setting_key']] = $row['setting_value'];
        }
    }

    // Get dynamic company/site name from settings
    $company_settings = [];
    $company_result = $db->query("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('company_name', 'site_name', 'email_from_name')");
    if ($company_result && $company_result->num_rows > 0) {
        while ($row = $company_result->fetch_assoc()) {
            $company_settings[$row['setting_key']] = $row['setting_value'];
        }
    }

    $site_name = $company_settings['site_name'] ?? $company_settings['company_name'] ?? 'LendSwift';
    $site_url = $site_settings['site_url'] ?? BASE_URL;

    // Always use a relative path for the login URL to ensure it works regardless of domain
    $login_url = '/pages/login.php';

    // If we're using a subdirectory installation (like pfloans.com), include it in the path
    if (strpos($_SERVER['REQUEST_URI'] ?? '', '/pfloans.com/') !== false) {
        $login_url = '/pfloans.com' . $login_url;
    }

    // Prepare email subject and content based on status
    $subject = "{$site_name} - Account Status Update";

    // Get site logo for email template - use dynamic logo from database
    $site_logo = get_setting('site_logo', '/assets/images/logo.svg');
    $logo_url = BASE_URL . $site_logo;
    $logo_exists = !empty($site_logo) && file_exists(BASE_PATH . $site_logo);

    // Start with common header
    $message = "
    <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border-radius: 5px;\">
        <div style=\"text-align: center; margin-bottom: 20px;\">";

    // Add logo or company name fallback
    if ($logo_exists) {
        $message .= "
            <img src=\"{$logo_url}\" alt=\"{$site_name} Logo\" style=\"max-width: 200px; max-height: 60px;\">";
    } else {
        $message .= "
            <div style=\"font-size: 24px; font-weight: bold; color: #4f46e5; margin: 10px 0;\">{$site_name}</div>";
    }

    $message .= "
        </div>
        <h2 style=\"color: #4f46e5; margin-bottom: 20px;\">Account Status Update</h2>
        <p>Dear " . htmlspecialchars($user['name']) . ",</p>
    ";

    // Get previous status if activating (to check if it was suspended)
    $previous_status = '';
    if ($new_status === 'active') {
        // Get the most recent status that is not 'active' (to find if it was suspended)
        $status_query = $db->prepare("SELECT status FROM user_status_history WHERE user_id = ? AND status != 'active' ORDER BY created_at DESC LIMIT 1");
        $status_query->bind_param("i", $user_id);
        $status_query->execute();
        $status_result = $status_query->get_result();

        if ($status_result && $status_result->num_rows > 0) {
            $previous_status = $status_result->fetch_assoc()['status'];
        }
    }

    // Add status-specific content
    if ($new_status === 'active') {
        if ($previous_status === 'suspended') {
            // Special message for unsuspending
            $message .= "
            <p>We are pleased to inform you that your account has been <strong style=\"color: #10b981;\">reactivated</strong> after being suspended.</p>
            <p>The suspension on your account has been lifted. You can now log in to your account and access all features of our platform again.</p>
            <p>Thank you for your patience during this process.</p>
            ";
        } else {
            // Regular activation message
            $message .= "
            <p>We are pleased to inform you that your account has been <strong style=\"color: #10b981;\">activated</strong>.</p>
            <p>You can now log in to your account and access all features of our platform.</p>
            ";
        }
    } elseif ($new_status === 'inactive') {
        $message .= "
        <p>We regret to inform you that your account has been <strong style=\"color: #ef4444;\">deactivated</strong>.</p>
        <p>You will not be able to log in to your account until it is reactivated by an administrator.</p>
        ";
    } elseif ($new_status === 'suspended') {
        $message .= "
        <p>We regret to inform you that your account has been <strong style=\"color: #f59e0b;\">suspended</strong>.</p>
        <p>Your account has been temporarily suspended. You will not be able to log in to your account until the suspension is lifted.</p>
        ";
    }

    // Add admin note if provided
    if (!empty($admin_note)) {
        $message .= "
        <div style=\"background-color: #f9f9f9; border-radius: 5px; padding: 15px; margin: 20px 0;\">
            <h3 style=\"color: #4f46e5; margin-top: 0;\">Message from Administrator</h3>
            <p>" . nl2br(htmlspecialchars($admin_note)) . "</p>
        </div>
        ";
    }

    // Add contact information
    $message .= "
        <p>If you have any questions or concerns regarding this change, please contact our support team.</p>
        <p>Best regards,<br>The {$site_name} Team</p>
        <div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666; text-align: center;\">
            <p>&copy; " . date('Y') . " {$site_name}. All rights reserved.</p>
        </div>
    </div>
    ";

    // Send email
    return send_notification_email($user['email'], $subject, $message);
}

/**
 * Send document upload notification email
 *
 * @param int $document_id Document ID
 * @return bool True if email was sent, false otherwise
 */
function send_document_upload_email($document_id) {
    $db = getDbConnection();

    // Get document details
    $stmt = $db->prepare("
        SELECT
            ad.*,
            la.loan_product_id,
            la.user_id,
            u.name AS user_name,
            u.email AS user_email,
            lp.name AS product_name
        FROM
            application_documents ad
        JOIN
            loan_applications la ON ad.application_id = la.id
        JOIN
            users u ON la.user_id = u.id
        JOIN
            loan_products lp ON la.loan_product_id = lp.id
        WHERE
            ad.id = ?
    ");

    $stmt->bind_param("i", $document_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if (!$result || $result->num_rows === 0) {
        return false;
    }

    $document = $result->fetch_assoc();

    // Get dynamic company/site name from settings
    $company_settings = [];
    $company_result = $db->query("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('company_name', 'site_name', 'email_from_name')");
    if ($company_result && $company_result->num_rows > 0) {
        while ($row = $company_result->fetch_assoc()) {
            $company_settings[$row['setting_key']] = $row['setting_value'];
        }
    }
    $site_name = $company_settings['site_name'] ?? $company_settings['company_name'] ?? 'LendSwift';

    // Prepare email content
    $subject = "Document Uploaded for Loan Application #" . $document['application_id'];

    $message = "
    <p>Dear " . htmlspecialchars($document['user_name']) . ",</p>

    <p>We have received your document upload for loan application #" . $document['application_id'] . " for " . htmlspecialchars($document['product_name']) . ".</p>

    <p><strong>Document Name:</strong> " . htmlspecialchars($document['file_name']) . "</p>
    <p><strong>Upload Date:</strong> " . date('F j, Y', strtotime($document['upload_date'])) . "</p>
    ";

    if ($document['status'] === 'pending') {
        $message .= "<p>Your document is currently under review. We will notify you once it has been processed.</p>";
    } elseif ($document['status'] === 'approved') {
        $message .= "<p>Your document has been approved.</p>";
    } elseif ($document['status'] === 'rejected') {
        $message .= "<p>Your document has been rejected. Please log in to your account for more information.</p>";
    }

    $message .= "
    <p>Thank you for your cooperation.</p>

    <p>Best regards,<br>
    The {$site_name} Team</p>
    ";

    // Send email
    return send_notification_email($document['user_email'], $subject, $message);
}
/**
 * Send loan application confirmation email
 *
 * @param int $application_id Application ID
 * @return bool True if email was sent, false otherwise
 */
function send_loan_application_confirmation_email($application_id) {
    $db = getDbConnection();

    // Get application details with all form data
    $stmt = $db->prepare("
        SELECT
            la.*,
            u.name AS user_name,
            u.email AS user_email,
            lp.name AS product_name,
            lp.interest_rate,
            lp.term_months,
            ls.name AS status_name,
            c.symbol AS currency_symbol,
            c.code AS currency_code
        FROM
            loan_applications la
        JOIN
            users u ON la.user_id = u.id
        JOIN
            loan_products lp ON la.loan_product_id = lp.id
        JOIN
            loan_statuses ls ON la.status_id = ls.id
        JOIN
            currencies c ON la.currency_id = c.id
        WHERE
            la.id = ?
    ");

    $stmt->bind_param("i", $application_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if (!$result || $result->num_rows === 0) {
        return false;
    }

    $application = $result->fetch_assoc();

    // Get dynamic company/site name from settings
    $company_settings = [];
    $company_result = $db->query("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('company_name', 'site_name', 'email_from_name')");
    if ($company_result && $company_result->num_rows > 0) {
        while ($row = $company_result->fetch_assoc()) {
            $company_settings[$row['setting_key']] = $row['setting_value'];
        }
    }
    $site_name = $company_settings['site_name'] ?? $company_settings['company_name'] ?? 'LendSwift';

    // Get form data
    $form_data = [];
    $stmt = $db->prepare("
        SELECT
            fd.value,
            ff.label,
            ff.name,
            ff.type
        FROM
            form_data fd
        JOIN
            form_fields ff ON fd.field_id = ff.id
        WHERE
            fd.application_id = ?
    ");

    $stmt->bind_param("i", $application_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $form_data[$row['label']] = $row['value'];
        }
    }

    // Calculate loan details
    $principal = $application['applied_amount'];
    $interest_rate = $application['interest_rate'];
    $term_months = $application['term_months'];

    // Calculate monthly payment
    $monthly_interest_rate = ($interest_rate / 100) / 12;
    $monthly_payment = $principal * $monthly_interest_rate * pow(1 + $monthly_interest_rate, $term_months) / (pow(1 + $monthly_interest_rate, $term_months) - 1);
    $total_payment = $monthly_payment * $term_months;
    $total_interest = $total_payment - $principal;

    // Prepare email content
    $subject = "Loan Application Confirmation - #" . $application_id;

    $message = "
    <p>Dear " . htmlspecialchars($application['user_name']) . ",</p>

    <p>Thank you for submitting your loan application. We have received your application and it is currently being processed by our team.</p>

    <div style='background-color: #f9f9f9; border-radius: 5px; padding: 15px; margin: 20px 0;'>
        <h3 style='color: #4f46e5; margin-top: 0;'>Application Details</h3>
        <table style='width: 100%; border-collapse: collapse;'>
            <tr>
                <td style='padding: 8px 0; border-bottom: 1px solid #eee; width: 40%;'><strong>Application ID:</strong></td>
                <td style='padding: 8px 0; border-bottom: 1px solid #eee;'>#" . $application_id . "</td>
            </tr>
            <tr>
                <td style='padding: 8px 0; border-bottom: 1px solid #eee;'><strong>Loan Product:</strong></td>
                <td style='padding: 8px 0; border-bottom: 1px solid #eee;'>" . htmlspecialchars($application['product_name']) . "</td>
            </tr>
            <tr>
                <td style='padding: 8px 0; border-bottom: 1px solid #eee;'><strong>Amount:</strong></td>
                <td style='padding: 8px 0; border-bottom: 1px solid #eee;'>" . $application['currency_symbol'] . number_format($application['applied_amount'], 2) . " " . $application['currency_code'] . "</td>
            </tr>
            <tr>
                <td style='padding: 8px 0; border-bottom: 1px solid #eee;'><strong>Interest Rate:</strong></td>
                <td style='padding: 8px 0; border-bottom: 1px solid #eee;'>" . $application['interest_rate'] . "%</td>
            </tr>
            <tr>
                <td style='padding: 8px 0; border-bottom: 1px solid #eee;'><strong>Term:</strong></td>
                <td style='padding: 8px 0; border-bottom: 1px solid #eee;'>" . $application['term_months'] . " months</td>
            </tr>
            <tr>
                <td style='padding: 8px 0; border-bottom: 1px solid #eee;'><strong>Purpose:</strong></td>
                <td style='padding: 8px 0; border-bottom: 1px solid #eee;'>" . htmlspecialchars($application['purpose']) . "</td>
            </tr>
            <tr>
                <td style='padding: 8px 0; border-bottom: 1px solid #eee;'><strong>Submission Date:</strong></td>
                <td style='padding: 8px 0; border-bottom: 1px solid #eee;'>" . date('F j, Y', strtotime($application['submission_date'])) . "</td>
            </tr>
            <tr>
                <td style='padding: 8px 0; border-bottom: 1px solid #eee;'><strong>Status:</strong></td>
                <td style='padding: 8px 0; border-bottom: 1px solid #eee;'>" . htmlspecialchars($application['status_name']) . "</td>
            </tr>
        </table>
    </div>

    <div style='background-color: #f9f9f9; border-radius: 5px; padding: 15px; margin: 20px 0;'>
        <h3 style='color: #4f46e5; margin-top: 0;'>Loan Calculation</h3>
        <table style='width: 100%; border-collapse: collapse;'>
            <tr>
                <td style='padding: 8px 0; border-bottom: 1px solid #eee; width: 40%;'><strong>Monthly Payment:</strong></td>
                <td style='padding: 8px 0; border-bottom: 1px solid #eee;'>" . $application['currency_symbol'] . number_format($monthly_payment, 2) . "</td>
            </tr>
            <tr>
                <td style='padding: 8px 0; border-bottom: 1px solid #eee;'><strong>Total Interest:</strong></td>
                <td style='padding: 8px 0; border-bottom: 1px solid #eee;'>" . $application['currency_symbol'] . number_format($total_interest, 2) . "</td>
            </tr>
            <tr>
                <td style='padding: 8px 0; border-bottom: 1px solid #eee;'><strong>Total Repayment:</strong></td>
                <td style='padding: 8px 0; border-bottom: 1px solid #eee;'>" . $application['currency_symbol'] . number_format($total_payment, 2) . "</td>
            </tr>
        </table>
        <p style='font-size: 0.8em; color: #666; margin-top: 10px;'>This is an estimate. Actual terms may vary.</p>
    </div>
    ";

    // Add form data if available
    if (!empty($form_data)) {
        $message .= "
        <div style='background-color: #f9f9f9; border-radius: 5px; padding: 15px; margin: 20px 0;'>
            <h3 style='color: #4f46e5; margin-top: 0;'>Additional Information</h3>
            <table style='width: 100%; border-collapse: collapse;'>
        ";

        foreach ($form_data as $label => $value) {
            $message .= "
                <tr>
                    <td style='padding: 8px 0; border-bottom: 1px solid #eee; width: 40%;'><strong>" . htmlspecialchars($label) . ":</strong></td>
                    <td style='padding: 8px 0; border-bottom: 1px solid #eee;'>" . htmlspecialchars($value) . "</td>
                </tr>
            ";
        }

        $message .= "
            </table>
        </div>
        ";
    }

    $message .= "
    <p>Our team will review your application and may contact you for additional information or documentation if needed. You can check the status of your application by logging into your account.</p>

    <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>

    <p>Thank you for choosing our services.</p>

    <p>Best regards,<br>
    The {$site_name} Team</p>
    ";

    // Send email
    return send_notification_email($application['user_email'], $subject, $message);
}
?>

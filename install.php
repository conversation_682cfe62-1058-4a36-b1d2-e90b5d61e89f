<?php
/**
 * LendSwift Database Configuration Installer
 * 
 * This script updates the database configuration in config files.
 * No SQL installation - assumes database is already set up online.
 * 
 * @package LendSwift
 * @version 1.0.0
 */

// Prevent direct access if already installed
if (file_exists(__DIR__ . '/.installed')) {
    die('Installation already completed. Delete .installed file to reinstall.');
}

// Start session
session_start();

// Installation key for security
$INSTALL_KEY = 'lendswift_2025_config';

// Get current step
$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$action = isset($_POST['action']) ? $_POST['action'] : '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    
    // Verify installation key
    if (!isset($_POST['install_key']) || $_POST['install_key'] !== $INSTALL_KEY) {
        $error = "Invalid installation key!";
    } else {
        
        switch ($action) {
            case 'test_connection':
                $result = testDatabaseConnection($_POST);
                break;
                
            case 'update_config':
                $result = updateDatabaseConfig($_POST);
                if ($result['success']) {
                    $step = 3; // Move to completion step
                }
                break;
        }
    }
}

/**
 * Test database connection
 */
function testDatabaseConnection($data) {
    try {
        $host = trim($data['db_host']);
        $user = trim($data['db_user']);
        $pass = trim($data['db_pass']);
        $name = trim($data['db_name']);
        
        // Test connection
        $mysqli = new mysqli($host, $user, $pass, $name);
        
        if ($mysqli->connect_error) {
            return [
                'success' => false,
                'message' => 'Connection failed: ' . $mysqli->connect_error
            ];
        }
        
        // Test if we can query the database
        $result = $mysqli->query("SHOW TABLES");
        $table_count = $result ? $result->num_rows : 0;
        
        $mysqli->close();
        
        return [
            'success' => true,
            'message' => "Connection successful! Found $table_count tables in database.",
            'table_count' => $table_count
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Error: ' . $e->getMessage()
        ];
    }
}

/**
 * Update database configuration in config files
 */
function updateDatabaseConfig($data) {
    try {
        $host = trim($data['db_host']);
        $user = trim($data['db_user']);
        $pass = trim($data['db_pass']);
        $name = trim($data['db_name']);
        
        // Config files to update
        $config_files = [
            'includes/core/config.php',
            'production/includes/core/config.php'
        ];
        
        $updated_files = [];
        $errors = [];
        
        foreach ($config_files as $file) {
            if (file_exists($file)) {
                
                // Read current config
                $content = file_get_contents($file);
                
                if ($content === false) {
                    $errors[] = "Could not read file: $file";
                    continue;
                }
                
                // Update database configuration
                $content = preg_replace(
                    "/define\('DB_HOST',\s*'[^']*'\);/",
                    "define('DB_HOST', '$host');",
                    $content
                );
                
                $content = preg_replace(
                    "/define\('DB_USER',\s*'[^']*'\);/",
                    "define('DB_USER', '$user');",
                    $content
                );
                
                $content = preg_replace(
                    "/define\('DB_PASS',\s*'[^']*'\);/",
                    "define('DB_PASS', '$pass');",
                    $content
                );
                
                $content = preg_replace(
                    "/define\('DB_NAME',\s*'[^']*'\);/",
                    "define('DB_NAME', '$name');",
                    $content
                );
                
                // Write updated config
                if (file_put_contents($file, $content) !== false) {
                    $updated_files[] = $file;
                } else {
                    $errors[] = "Could not write to file: $file";
                }
            }
        }
        
        if (empty($updated_files)) {
            return [
                'success' => false,
                'message' => 'No config files found to update!'
            ];
        }
        
        if (!empty($errors)) {
            return [
                'success' => false,
                'message' => 'Some files could not be updated: ' . implode(', ', $errors)
            ];
        }
        
        // Create installation marker
        file_put_contents(__DIR__ . '/.installed', date('Y-m-d H:i:s') . "\nDatabase configuration updated successfully.");
        
        return [
            'success' => true,
            'message' => 'Database configuration updated successfully!',
            'updated_files' => $updated_files
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Error updating configuration: ' . $e->getMessage()
        ];
    }
}

/**
 * Get current database configuration
 */
function getCurrentConfig() {
    $config = [
        'host' => 'localhost',
        'user' => 'root',
        'pass' => 'root',
        'name' => 'loan'
    ];
    
    // Try to read from existing config
    $config_files = ['includes/core/config.php', 'production/includes/core/config.php'];
    
    foreach ($config_files as $file) {
        if (file_exists($file)) {
            $content = file_get_contents($file);
            
            if (preg_match("/define\('DB_HOST',\s*'([^']+)'\)/", $content, $matches)) {
                $config['host'] = $matches[1];
            }
            if (preg_match("/define\('DB_USER',\s*'([^']+)'\)/", $content, $matches)) {
                $config['user'] = $matches[1];
            }
            if (preg_match("/define\('DB_PASS',\s*'([^']+)'\)/", $content, $matches)) {
                $config['pass'] = $matches[1];
            }
            if (preg_match("/define\('DB_NAME',\s*'([^']+)'\)/", $content, $matches)) {
                $config['name'] = $matches[1];
            }
            break;
        }
    }
    
    return $config;
}

$current_config = getCurrentConfig();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LendSwift Database Configuration Installer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .installer {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 100%;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 8px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 14px;
        }
        
        .content {
            padding: 30px;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e5e7eb;
            color: #6b7280;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin: 0 10px;
            position: relative;
        }
        
        .step.active {
            background: #4f46e5;
            color: white;
        }
        
        .step.completed {
            background: #10b981;
            color: white;
        }
        
        .step:not(:last-child)::after {
            content: '';
            position: absolute;
            left: 100%;
            top: 50%;
            width: 20px;
            height: 2px;
            background: #e5e7eb;
            transform: translateY(-50%);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #4f46e5;
        }
        
        .btn {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.2s;
            width: 100%;
        }
        
        .btn:hover {
            background: #4338ca;
        }
        
        .btn-secondary {
            background: #6b7280;
            margin-right: 10px;
            width: auto;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .alert {
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        
        .alert-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
        
        .alert-info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }
        
        .current-config {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 20px;
        }
        
        .current-config h3 {
            color: #374151;
            margin-bottom: 12px;
            font-size: 16px;
        }
        
        .config-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .config-item:last-child {
            border-bottom: none;
        }
        
        .config-label {
            font-weight: 600;
            color: #6b7280;
        }
        
        .config-value {
            color: #374151;
            font-family: monospace;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        
        .success-icon {
            width: 60px;
            height: 60px;
            background: #10b981;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 24px;
        }
    </style>
</head>
<body>
    <div class="installer">
        <div class="header">
            <h1>LendSwift Database Installer</h1>
            <p>Update database configuration for your LendSwift installation</p>
        </div>
        
        <div class="content">
            <div class="step-indicator">
                <div class="step <?php echo $step >= 1 ? 'active' : ''; ?><?php echo $step > 1 ? ' completed' : ''; ?>">1</div>
                <div class="step <?php echo $step >= 2 ? 'active' : ''; ?><?php echo $step > 2 ? ' completed' : ''; ?>">2</div>
                <div class="step <?php echo $step >= 3 ? 'active' : ''; ?>">3</div>
            </div>

            <?php if (isset($error)): ?>
                <div class="alert alert-error">
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <?php if (isset($result)): ?>
                <div class="alert <?php echo $result['success'] ? 'alert-success' : 'alert-error'; ?>">
                    <?php echo htmlspecialchars($result['message']); ?>
                </div>
            <?php endif; ?>

            <?php if ($step == 1): ?>
                <!-- Step 1: Show Current Configuration -->
                <h2>Step 1: Current Database Configuration</h2>
                <p>Below is your current database configuration. You can update it to connect to your online database.</p>

                <div class="current-config">
                    <h3>Current Configuration</h3>
                    <div class="config-item">
                        <span class="config-label">Database Host:</span>
                        <span class="config-value"><?php echo htmlspecialchars($current_config['host']); ?></span>
                    </div>
                    <div class="config-item">
                        <span class="config-label">Database User:</span>
                        <span class="config-value"><?php echo htmlspecialchars($current_config['user']); ?></span>
                    </div>
                    <div class="config-item">
                        <span class="config-label">Database Password:</span>
                        <span class="config-value"><?php echo str_repeat('*', strlen($current_config['pass'])); ?></span>
                    </div>
                    <div class="config-item">
                        <span class="config-label">Database Name:</span>
                        <span class="config-value"><?php echo htmlspecialchars($current_config['name']); ?></span>
                    </div>
                </div>

                <div class="alert alert-info">
                    <strong>Note:</strong> This installer will update your database configuration to connect to your online database.
                    Make sure your database is already set up with the LendSwift tables and data.
                </div>

                <a href="?step=2" class="btn">Continue to Database Setup</a>

            <?php elseif ($step == 2): ?>
                <!-- Step 2: Database Configuration Form -->
                <h2>Step 2: Update Database Configuration</h2>
                <p>Enter your new database connection details below:</p>

                <form method="POST">
                    <input type="hidden" name="action" value="update_config">

                    <div class="form-group">
                        <label for="install_key">Installation Key:</label>
                        <input type="text" id="install_key" name="install_key" placeholder="Enter installation key" required>
                        <small style="color: #6b7280; font-size: 12px;">Key: <?php echo $INSTALL_KEY; ?></small>
                    </div>

                    <div class="form-group">
                        <label for="db_host">Database Host:</label>
                        <input type="text" id="db_host" name="db_host" value="<?php echo htmlspecialchars($current_config['host']); ?>" required>
                    </div>

                    <div class="form-group">
                        <label for="db_user">Database Username:</label>
                        <input type="text" id="db_user" name="db_user" value="<?php echo htmlspecialchars($current_config['user']); ?>" required>
                    </div>

                    <div class="form-group">
                        <label for="db_pass">Database Password:</label>
                        <input type="password" id="db_pass" name="db_pass" value="<?php echo htmlspecialchars($current_config['pass']); ?>">
                    </div>

                    <div class="form-group">
                        <label for="db_name">Database Name:</label>
                        <input type="text" id="db_name" name="db_name" value="<?php echo htmlspecialchars($current_config['name']); ?>" required>
                    </div>

                    <div class="button-group">
                        <button type="submit" name="action" value="test_connection" class="btn btn-secondary">Test Connection</button>
                        <button type="submit" class="btn">Update Configuration</button>
                    </div>
                </form>

            <?php elseif ($step == 3): ?>
                <!-- Step 3: Installation Complete -->
                <div class="success-icon">✓</div>
                <h2 style="text-align: center; margin-bottom: 20px;">Installation Complete!</h2>

                <div class="alert alert-success">
                    Your database configuration has been updated successfully. The LendSwift system is now ready to use.
                </div>

                <?php if (isset($result['updated_files'])): ?>
                    <div class="current-config">
                        <h3>Updated Configuration Files</h3>
                        <?php foreach ($result['updated_files'] as $file): ?>
                            <div class="config-item">
                                <span class="config-label">✓</span>
                                <span class="config-value"><?php echo htmlspecialchars($file); ?></span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>

                <div class="alert alert-info">
                    <strong>Important:</strong> The install.php file has been secured with a .installed marker.
                    Delete the .installed file if you need to run the installer again.
                </div>

                <div class="button-group">
                    <a href="index.php" class="btn">Go to Main Application</a>
                    <a href="production/" class="btn btn-secondary">Go to Production</a>
                </div>

            <?php endif; ?>
        </div>
    </div>
</body>
</html>

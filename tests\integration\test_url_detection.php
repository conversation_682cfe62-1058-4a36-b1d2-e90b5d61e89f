<?php
/**
 * URL Detection Test
 *
 * This file tests the dynamic URL detection functionality
 */

// Define LENDSWIFT constant to prevent direct access error
define('LENDSWIFT', true);

// Include the configuration
require_once 'includes/core/config.php';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL Detection Test - LendSwift Production</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .test-result { background: #f5f5f5; padding: 20px; border-radius: 5px; margin: 10px 0; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        h1 { color: #333; }
        code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>LendSwift Production - URL Detection Test</h1>
    
    <div class="test-result success">
        <h3>✅ Dynamic URL Detection Working</h3>
        <p><strong>Detected BASE_URL:</strong> <code><?php echo BASE_URL; ?></code></p>
        <p><strong>Assets URL:</strong> <code><?php echo ASSETS_URL; ?></code></p>
        <p><strong>Uploads URL:</strong> <code><?php echo UPLOADS_URL; ?></code></p>
    </div>
    
    <div class="test-result info">
        <h3>📋 Server Information</h3>
        <p><strong>Server Name:</strong> <code><?php echo $_SERVER['HTTP_HOST'] ?? 'N/A'; ?></code></p>
        <p><strong>Script Name:</strong> <code><?php echo $_SERVER['SCRIPT_NAME'] ?? 'N/A'; ?></code></p>
        <p><strong>Request URI:</strong> <code><?php echo $_SERVER['REQUEST_URI'] ?? 'N/A'; ?></code></p>
        <p><strong>Protocol:</strong> <code><?php echo (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'HTTPS' : 'HTTP'; ?></code></p>
    </div>
    
    <div class="test-result info">
        <h3>🔧 Configuration Status</h3>
        <p><strong>Development Mode:</strong> <code><?php echo DEVELOPMENT_MODE ? 'TRUE (Development)' : 'FALSE (Production)'; ?></code></p>
        <p><strong>Site Name:</strong> <code><?php echo SITE_NAME; ?></code></p>
        <p><strong>Database Name:</strong> <code><?php echo DB_NAME; ?></code></p>
    </div>
    
    <div class="test-result">
        <h3>🚀 Next Steps</h3>
        <ul>
            <li>If the BASE_URL looks correct, the dynamic detection is working properly</li>
            <li>You can now deploy this production version to any domain/subdirectory</li>
            <li>Delete this test file (<code>test_url_detection.php</code>) when ready for production</li>
            <li>Run the installer or import the database to complete setup</li>
        </ul>
    </div>
    
    <p><a href="index.php">← Go to Main Application</a></p>
</body>
</html>
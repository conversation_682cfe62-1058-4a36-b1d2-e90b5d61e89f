<?php
/**
 * Standalone Fix for Double Admin URLs
 * 
 * This script fixes the double admin/admin/ issue without requiring 
 * the full config system - works independently
 */

/**
 * Fix admin links in a file
 */
function fixAdminLinksInFile($file_path) {
    if (!file_exists($file_path)) {
        return ['success' => false, 'message' => 'File not found: ' . $file_path];
    }
    
    $content = file_get_contents($file_path);
    if ($content === false) {
        return ['success' => false, 'message' => 'Could not read file: ' . $file_path];
    }
    
    $original_content = $content;
    $changes = 0;
    
    // Patterns to fix - more comprehensive list
    $patterns = [
        // Form actions
        '/action="<\?php echo BASE_URL; \?>\/admin\/([^"]+)"/' => 'action="$1"',
        
        // Href links
        '/href="<\?php echo BASE_URL; \?>\/admin\/([^"]+)"/' => 'href="$1"',
        
        // Redirect calls
        '/redirect\(BASE_URL \. \'\/admin\/([^\']+)\'\)/' => 'redirect(\'$1\')',
        
        // JavaScript/AJAX URLs
        '/BASE_URL \. \'\/admin\/([^\']+)\'/' => '\'$1\'',
        
        // Double quotes version
        '/BASE_URL \. "\/admin\/([^"]+)"/' => '"$1"',
        
        // Additional patterns for edge cases
        '/\<\?php echo BASE_URL; \?\>\/admin\/([a-zA-Z0-9\-_\.]+\.php)/' => '$1',
        
        // Location header redirects
        '/Location: " \. BASE_URL \. "\/admin\/([^"]+)"/' => 'Location: "$1"',
        
        // Window.location in JavaScript
        '/window\.location\.href = "<\?php echo BASE_URL; \?>\/admin\/([^"]+)"/' => 'window.location.href = "$1"',
    ];
    
    foreach ($patterns as $pattern => $replacement) {
        $new_content = preg_replace($pattern, $replacement, $content);
        if ($new_content !== $content) {
            $pattern_matches = preg_match_all($pattern, $content, $matches);
            $changes += $pattern_matches;
            $content = $new_content;
        }
    }
    
    if ($content !== $original_content) {
        // Create backup
        $backup_file = $file_path . '.backup.' . date('Y-m-d-H-i-s');
        file_put_contents($backup_file, $original_content);
        
        // Write updated content
        if (file_put_contents($file_path, $content) !== false) {
            return [
                'success' => true, 
                'message' => "Fixed $changes admin links in " . basename($file_path),
                'changes' => $changes,
                'backup' => $backup_file
            ];
        } else {
            return ['success' => false, 'message' => 'Could not write to file: ' . $file_path];
        }
    } else {
        return [
            'success' => true, 
            'message' => 'No admin links to fix in ' . basename($file_path),
            'changes' => 0
        ];
    }
}

/**
 * Get all PHP files in admin directory
 */
function getAdminPhpFiles() {
    $admin_dir = 'admin';
    $files = [];
    
    if (is_dir($admin_dir)) {
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($admin_dir)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'php') {
                $files[] = $file->getPathname();
            }
        }
    }
    
    return $files;
}

// Get current domain URL for display
function getCurrentUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
    $host = $_SERVER['HTTP_HOST'];
    $script = $_SERVER['SCRIPT_NAME'];
    $path = preg_replace('#/[^/]*$#', '', $script);
    return $protocol . $host . $path;
}

// Main execution
$results = [];
$total_changes = 0;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['fix_all_admin_files'])) {
        $admin_files = getAdminPhpFiles();
        
        foreach ($admin_files as $file) {
            $result = fixAdminLinksInFile($file);
            $results[] = $result;
            if ($result['success'] && isset($result['changes'])) {
                $total_changes += $result['changes'];
            }
        }
    } elseif (isset($_POST['fix_single_file']) && !empty($_POST['file_path'])) {
        $file_path = $_POST['file_path'];
        $result = fixAdminLinksInFile($file_path);
        $results[] = $result;
        if ($result['success'] && isset($result['changes'])) {
            $total_changes += $result['changes'];
        }
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Double Admin URLs - Standalone</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .result { padding: 15px; border-radius: 5px; margin: 10px 0; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        h1 { color: #333; text-align: center; }
        .button { background: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; font-size: 16px; }
        .button:hover { background: #0056b3; }
        .button-success { background: #28a745; }
        .button-success:hover { background: #218838; }
        code { background: #f8f9fa; padding: 2px 6px; border-radius: 3px; font-family: monospace; }
        .file-list { max-height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: #f9f9f9; }
        .stats { display: flex; justify-content: space-around; text-align: center; margin: 20px 0; }
        .stat { background: #f8f9fa; padding: 15px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Fix Double Admin URLs</h1>
        
        <div class="result info">
            <h3>📋 Current Status</h3>
            <p><strong>Current URL:</strong> <code><?php echo getCurrentUrl(); ?></code></p>
            <p><strong>Problem:</strong> Admin pages using <code>BASE_URL . '/admin/'</code> create double admin paths</p>
            <p><strong>Solution:</strong> Replace with relative paths like <code>href="users.php"</code></p>
        </div>

        <?php if (!empty($results)): ?>
            <div class="result <?php echo $total_changes > 0 ? 'success' : 'warning'; ?>">
                <h3>🎉 Fix Results</h3>
                <div class="stats">
                    <div class="stat">
                        <strong><?php echo count($results); ?></strong><br>
                        Files Processed
                    </div>
                    <div class="stat">
                        <strong><?php echo $total_changes; ?></strong><br>
                        Links Fixed
                    </div>
                    <div class="stat">
                        <strong><?php echo count(array_filter($results, function($r) { return $r['success']; })); ?></strong><br>
                        Successful
                    </div>
                </div>
                
                <h4>Detailed Results:</h4>
                <?php foreach ($results as $result): ?>
                    <div class="result <?php echo $result['success'] ? 'success' : 'error'; ?>">
                        <?php echo $result['message']; ?>
                        <?php if (isset($result['backup'])): ?>
                            <br><small>📁 Backup: <?php echo basename($result['backup']); ?></small>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <div class="result">
            <h3>🚀 Fix All Admin Files</h3>
            <p>This will scan and fix all PHP files in the admin directory:</p>
            
            <form method="POST" style="text-align: center;">
                <button type="submit" name="fix_all_admin_files" class="button button-success">
                    🔧 Fix All Admin Files Now
                </button>
            </form>
        </div>

        <div class="result info">
            <h3>📁 Files to be Fixed</h3>
            <div class="file-list">
                <?php 
                $admin_files = getAdminPhpFiles();
                foreach ($admin_files as $file): 
                ?>
                    <div>📄 <?php echo htmlspecialchars($file); ?></div>
                <?php endforeach; ?>
            </div>
            <p><strong>Total Files Found:</strong> <?php echo count($admin_files); ?></p>
        </div>
        
        <div class="result">
            <h3>🧪 After Fixing</h3>
            <p>Once fixed, test these pages:</p>
            <ul>
                <li><a href="admin/add-user.php" target="_blank">Add User Page</a></li>
                <li><a href="admin/users.php" target="_blank">Users List</a></li>
                <li><a href="admin/index.php" target="_blank">Admin Dashboard</a></li>
            </ul>
        </div>

        <div class="result warning">
            <h3>⚠️ Important Notes</h3>
            <ul>
                <li>Backups are automatically created for all modified files</li>
                <li>This fix changes <code>BASE_URL . '/admin/'</code> to relative paths</li>
                <li>Test your admin functionality after running the fix</li>
                <li>You can safely run this multiple times</li>
            </ul>
        </div>
    </div>
</body>
</html>

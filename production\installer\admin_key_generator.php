<?php
/**
 * LendSwift License Key Generator - SUPER ADMIN ONLY
 * 
 * This file is for the super admin to generate license keys for customers
 * DO NOT DISTRIBUTE THIS FILE TO CUSTOMERS
 * 
 * @package LendSwift
 * @version 1.0
 * @access SUPER_ADMIN_ONLY
 */

// Simple password protection
$admin_password = 'LendSwift2025Admin!'; // Change this password
$entered_password = $_POST['admin_password'] ?? $_GET['pass'] ?? '';

if ($entered_password !== $admin_password) {
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>LendSwift License Generator - Admin Access</title>
        <style>
            body { font-family: Arial, sans-serif; background: #f5f5f5; padding: 50px; }
            .login-box { background: white; padding: 30px; border-radius: 10px; max-width: 400px; margin: 0 auto; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            input { width: 100%; padding: 10px; margin: 10px 0; border: 1px solid #ddd; border-radius: 5px; }
            button { background: #e74c3c; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; width: 100%; }
        </style>
    </head>
    <body>
        <div class="login-box">
            <h2>🔐 Super Admin Access Required</h2>
            <p>This tool is restricted to super administrators only.</p>
            <form method="POST">
                <input type="password" name="admin_password" placeholder="Enter admin password" required>
                <button type="submit">Access License Generator</button>
            </form>
        </div>
    </body>
    </html>
    <?php
    exit;
}

/**
 * Generate server-specific license key
 */
function generateServerKey($server_name, $document_root = '', $hostname = '', $os = '') {
    $server_data = [
        $server_name ?: 'localhost',
        $document_root ?: '/var/www/html',
        $hostname ?: 'server',
        $os ?: 'linux'
    ];
    
    $server_signature = implode('|', $server_data);
    return strtoupper(substr(hash('sha256', $server_signature . 'LENDSWIFT_LICENSE_2025'), 0, 20));
}

/**
 * Generate random license key
 */
function generateRandomKey() {
    $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $key = '';
    for ($i = 0; $i < 20; $i++) {
        $key .= $characters[random_int(0, strlen($characters) - 1)];
    }
    return $key;
}

// Handle key generation
$generated_keys = [];
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['generate_type'])) {
    switch ($_POST['generate_type']) {
        case 'server_specific':
            $server_name = $_POST['server_name'] ?? '';
            $document_root = $_POST['document_root'] ?? '';
            $hostname = $_POST['hostname'] ?? '';
            $os = $_POST['os'] ?? '';
            
            if ($server_name) {
                $key = generateServerKey($server_name, $document_root, $hostname, $os);
                $generated_keys[] = [
                    'type' => 'Server-Specific',
                    'key' => $key,
                    'server' => $server_name,
                    'details' => "Server: $server_name, Root: $document_root, Host: $hostname, OS: $os"
                ];
            }
            break;
            
        case 'random':
            $count = min(10, max(1, (int)($_POST['key_count'] ?? 1)));
            for ($i = 0; $i < $count; $i++) {
                $key = generateRandomKey();
                $generated_keys[] = [
                    'type' => 'Random',
                    'key' => $key,
                    'server' => 'Any',
                    'details' => 'Universal license key'
                ];
            }
            break;
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LendSwift License Key Generator - Super Admin</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            min-height: 100vh;
            padding: 20px;
            margin: 0;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .generator-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            margin: 20px 0;
            border-left: 4px solid #e74c3c;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .btn {
            background: #e74c3c;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        
        .btn:hover {
            background: #c0392b;
        }
        
        .btn-success {
            background: #27ae60;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .key-display {
            background: white;
            border: 2px solid #e74c3c;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 16px;
            font-weight: bold;
            text-align: center;
            color: #e74c3c;
            letter-spacing: 2px;
        }
        
        .key-list {
            margin-top: 20px;
        }
        
        .key-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .key-info {
            flex: 1;
        }
        
        .key-value {
            font-family: 'Courier New', monospace;
            font-size: 18px;
            font-weight: bold;
            color: #e74c3c;
            letter-spacing: 1px;
        }
        
        .key-details {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .copy-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .master-keys {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .warning {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            color: #721c24;
        }
        
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔑 LendSwift License Key Generator</h1>
            <p>Super Administrator Tool</p>
            <p><strong>CONFIDENTIAL - DO NOT DISTRIBUTE</strong></p>
        </div>
        
        <div class="content">
            <div class="warning">
                <strong>⚠️ SECURITY WARNING:</strong> This tool generates license keys for LendSwift installations. 
                Keep this file secure and do not distribute it to customers. Each key controls access to the software.
            </div>
            
            <div class="master-keys">
                <h4>🎯 Master License Keys (Always Valid)</h4>
                <div class="key-display">LENDSWIFT2025MASTER01</div>
                <div class="key-display">LENDSWIFT2025MASTER02</div>
                <p><small>These master keys work on any server and can be used for testing or emergency access.</small></p>
            </div>
            
            <div class="grid">
                <div class="generator-section">
                    <h3>🖥️ Server-Specific Keys</h3>
                    <p>Generate keys tied to specific server environments</p>
                    
                    <form method="POST">
                        <input type="hidden" name="admin_password" value="<?php echo htmlspecialchars($admin_password); ?>">
                        <input type="hidden" name="generate_type" value="server_specific">
                        
                        <div class="form-group">
                            <label>Server Name/Domain:</label>
                            <input type="text" name="server_name" placeholder="example.com" required>
                        </div>
                        
                        <div class="form-group">
                            <label>Document Root (optional):</label>
                            <input type="text" name="document_root" placeholder="/var/www/html">
                        </div>
                        
                        <div class="form-group">
                            <label>Hostname (optional):</label>
                            <input type="text" name="hostname" placeholder="server1">
                        </div>
                        
                        <div class="form-group">
                            <label>Operating System (optional):</label>
                            <input type="text" name="os" placeholder="Linux">
                        </div>
                        
                        <button type="submit" class="btn">Generate Server Key</button>
                    </form>
                </div>
                
                <div class="generator-section">
                    <h3>🎲 Random Universal Keys</h3>
                    <p>Generate random keys that work on any server</p>
                    
                    <form method="POST">
                        <input type="hidden" name="admin_password" value="<?php echo htmlspecialchars($admin_password); ?>">
                        <input type="hidden" name="generate_type" value="random">
                        
                        <div class="form-group">
                            <label>Number of Keys:</label>
                            <select name="key_count">
                                <option value="1">1 Key</option>
                                <option value="5">5 Keys</option>
                                <option value="10">10 Keys</option>
                            </select>
                        </div>
                        
                        <button type="submit" class="btn">Generate Random Keys</button>
                    </form>
                </div>
            </div>
            
            <?php if (!empty($generated_keys)): ?>
                <div class="key-list">
                    <h3>🔑 Generated License Keys</h3>
                    <?php foreach ($generated_keys as $key_data): ?>
                        <div class="key-item">
                            <div class="key-info">
                                <div class="key-value"><?php echo htmlspecialchars($key_data['key']); ?></div>
                                <div class="key-details">
                                    Type: <?php echo htmlspecialchars($key_data['type']); ?> | 
                                    Server: <?php echo htmlspecialchars($key_data['server']); ?><br>
                                    <?php echo htmlspecialchars($key_data['details']); ?>
                                </div>
                            </div>
                            <button class="copy-btn" onclick="copyToClipboard('<?php echo $key_data['key']; ?>')">
                                📋 Copy
                            </button>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
            
            <div class="generator-section">
                <h3>📋 Usage Instructions</h3>
                <ol>
                    <li><strong>Generate Keys:</strong> Use the forms above to create license keys for customers</li>
                    <li><strong>Distribute Keys:</strong> Provide the generated key to the customer</li>
                    <li><strong>Customer Installation:</strong> Customer enters the key during LendSwift installation</li>
                    <li><strong>Key Validation:</strong> The installer validates the key before allowing installation</li>
                </ol>
                
                <p><strong>Key Types:</strong></p>
                <ul>
                    <li><strong>Master Keys:</strong> Work on any server, use for testing or emergency access</li>
                    <li><strong>Server-Specific:</strong> Tied to specific server environment, more secure</li>
                    <li><strong>Random Universal:</strong> Work on any server, good for general distribution</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                alert('License key copied to clipboard!');
            }).catch(function(err) {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('License key copied to clipboard!');
            });
        }
    </script>
</body>
</html>

# LendSwift - Professional Loan Management System

**Version:** 1.0.0
**License:** Commercial
**Author:** LendSwift Team

A comprehensive, secure, and professional loan management system designed for financial institutions, lending companies, and loan brokers. Built with modern PHP, responsive design, and enterprise-level security features.

## 🚀 Features

### **Core Functionality**
- **Complete Loan Management**: Application processing, approval workflows, document management
- **User Dashboard**: Intuitive interface for loan applicants to track their applications
- **Admin Panel**: Comprehensive administrative interface with full system control
- **Payment Processing**: Integrated Stripe and PayPal payment gateways
- **Document Management**: Secure file upload and document handling system
- **Email System**: Professional email templates with automated notifications
- **Reporting & Analytics**: Detailed reports and system analytics

### **Security Features**
- **CSRF Protection**: Token-based cross-site request forgery protection
- **SQL Injection Prevention**: PDO prepared statements throughout
- **Secure Authentication**: Bcrypt password hashing and secure sessions
- **File Upload Security**: Restricted file types and validation
- **Input Validation**: Comprehensive form validation and sanitization

### **Technical Features**
- **Responsive Design**: Mobile-first, works on all devices
- **Dynamic URL Detection**: Works on any domain/subdirectory automatically
- **Professional Installer**: Secure installation with license key validation
- **Database Optimization**: Efficient database structure with proper indexing
- **Modern PHP**: Built with PHP 7.4+ using modern practices

### 3. Production Settings
- **File**: `includes/core/config.php`
- **Change**: Set `DEVELOPMENT_MODE` to `false`
- **Benefit**: Optimized for production environment

### 4. Google Translate Integration
- **File**: `includes/header.php`
- **Change**: Added Google Translate widget to navigation (desktop and mobile)
- **Languages**: English, Spanish, French, German, Chinese, Portuguese, Russian, Arabic, Italian, Japanese, Korean, Hindi, Dutch, Swedish, Norwegian, Danish, Finnish, Thai (18 total languages)
- **Benefit**: Multi-language support across all pages with expanded global coverage

## Installation Instructions

1. **Upload Files**: Upload all files to your web server
2. **Set Permissions**: Ensure `uploads/` directory is writable (755 or 777)
3. **Database Setup**: Run the installation script or import the SQL files
4. **Configuration**: Update database credentials in `includes/core/config.php` if needed

## Dynamic URL Detection

The system automatically detects:
- Protocol (HTTP/HTTPS)
- Domain name
- Subdirectory path
- Port (if non-standard)

This means the application will work correctly whether installed at:
- `https://yourdomain.com/`
- `https://yourdomain.com/loans/`
- `http://localhost/myproject/`
- Any other path configuration

## Files Structure

```
production/
├── index.php              # Main entry point
├── config.php             # Root configuration bridge
├── composer.json/lock     # PHP dependencies
├── admin/                 # Admin panel
├── ajax/                  # AJAX endpoints
├── assets/                # CSS, JS, images
├── includes/              # PHP includes and functions
├── pages/                 # Application pages
├── sql/                   # Database scripts
├── uploads/               # File upload directories
└── vendor/                # Composer dependencies
```

## Database Configuration

Update the following constants in `includes/core/config.php`:
- `DB_HOST` - Database host
- `DB_USER` - Database username
- `DB_PASS` - Database password
- `DB_NAME` - Database name

## Security Notes

- `DEVELOPMENT_MODE` is set to `false` for production
- Error reporting is minimized
- All file paths are dynamically calculated
- No hardcoded URLs that could expose development paths

## Support

This production version maintains full compatibility with the development version while providing enhanced portability and security for production deployments.

## 📋 System Requirements

- **PHP**: 7.4 or higher
- **MySQL**: 5.7+ or MariaDB 10.2+
- **Web Server**: Apache, Nginx, or IIS
- **PHP Extensions**: PDO MySQL, mbstring, OpenSSL, cURL
- **Memory**: 128MB minimum (256MB recommended)
- **Storage**: 50MB minimum

## 🚀 Quick Installation Guide

### **Step 1: Upload Files**
Upload all files to your web server's document root or subdirectory.

### **Step 2: Database Setup**
1. Create a MySQL database
2. Import the SQL files from the `/sql/` directory
3. Note your database credentials

### **Step 3: Run Installer**
1. Navigate to `http://yourdomain.com/installer/install.php`
2. Enter your license key (provided with purchase)
3. Configure database settings
4. Complete the installation wizard

### **Step 4: Access Your System**
- **User Interface**: `http://yourdomain.com/`
- **Admin Panel**: `http://yourdomain.com/admin/`
- **Default Admin**: `<EMAIL>` / `admin123`

⚠️ **Important**: Change default admin credentials immediately after installation.

## 🛡️ Security & License

This software is protected by license key validation and includes enterprise-level security features. See LICENSE.txt for full terms.

## 📞 Support

- 6 months free email support included
- Installation assistance available
- Documentation and user guides provided
- Updates and bug fixes for 12 months

---

**LendSwift** - Professional Loan Management Made Simple
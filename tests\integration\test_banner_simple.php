<?php
/**
 * Simple Banner Test
 * Quick test to verify notification banner system works
 */

// Define LENDSWIFT constant to prevent direct access to included files
define('LENDSWIFT', true);

// Include initialization file
require_once 'includes/init.php';

// Check if user is logged in
if (!is_user_logged_in()) {
    echo "Please log in first: <a href='login.php'>Login</a>";
    exit;
}

// Get user information safely
$user_id = get_current_user_id();
$user_name = $_SESSION['user_name'] ?? 'User';
$user_email = $_SESSION['user_email'] ?? '<EMAIL>';

// Get unread notifications count safely
$unread_notifications = 0;
try {
    $unread_notifications = get_unread_notifications_count($user_id);
} catch (Exception $e) {
    error_log('Error getting notification count: ' . $e->getMessage());
}

$page_title = 'Banner Test';
$current_page = 'test';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Banner Test - <?php echo SITE_NAME; ?></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="<?php echo ASSETS_URL; ?>/css/dashboard.css">
    <link rel="stylesheet" href="<?php echo ASSETS_URL; ?>/css/notification-banner.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    
    <style>
        .test-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #4f46e5;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-button:hover {
            background: #4338ca;
        }
        .test-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Notification Banner Test</h1>
        
        <div class="test-info">
            <p><strong>User:</strong> <?php echo htmlspecialchars($user_name); ?></p>
            <p><strong>Current Unread Notifications:</strong> <?php echo $unread_notifications; ?></p>
            <p><strong>User Type:</strong> <span id="user-type">Loading...</span></p>
        </div>
        
        <h3>Test Controls:</h3>
        <button class="test-button" onclick="testNewNotification()">
            <i class="fas fa-bell"></i> Test New Notification
        </button>
        <button class="test-button" onclick="testSuccess()">
            <i class="fas fa-check"></i> Test Success
        </button>
        <button class="test-button" onclick="testError()">
            <i class="fas fa-times"></i> Test Error
        </button>
        <button class="test-button" onclick="testInfo()">
            <i class="fas fa-info"></i> Test Info
        </button>
        <button class="test-button" onclick="createNotification()">
            <i class="fas fa-plus"></i> Create Real Notification
        </button>
        
        <div id="log" style="margin-top: 20px; padding: 15px; background: #f1f1f1; border-radius: 5px; font-family: monospace; min-height: 100px;">
            <strong>Test Log:</strong><br>
            Ready to test...<br>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="<?php echo ASSETS_URL; ?>/js/notification-banner.js"></script>
    
    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function testNewNotification() {
            log('Testing new notification banner...');
            if (window.notificationBanner) {
                window.notificationBanner.showNewNotificationBanner(1);
                log('✓ New notification banner shown');
            } else {
                log('✗ Banner system not ready');
            }
        }
        
        function testSuccess() {
            log('Testing success banner...');
            if (window.notificationBanner) {
                window.notificationBanner.showSuccess('Test successful!');
                log('✓ Success banner shown');
            } else {
                log('✗ Banner system not ready');
            }
        }
        
        function testError() {
            log('Testing error banner...');
            if (window.notificationBanner) {
                window.notificationBanner.showError('Test error message!');
                log('✓ Error banner shown');
            } else {
                log('✗ Banner system not ready');
            }
        }
        
        function testInfo() {
            log('Testing info banner...');
            if (window.notificationBanner) {
                window.notificationBanner.showInfo('Test info message!');
                log('✓ Info banner shown');
            } else {
                log('✗ Banner system not ready');
            }
        }
        
        async function createNotification() {
            log('Creating real notification...');
            try {
                const response = await fetch('ajax/create-test-notification.php', {
                    method: 'POST',
                    credentials: 'same-origin'
                });
                const data = await response.json();
                if (data.success) {
                    log('✓ Real notification created');
                    // Trigger manual banner
                    document.dispatchEvent(new CustomEvent('showNotificationBanner', {
                        detail: { type: 'new', count: 1 }
                    }));
                } else {
                    log('✗ Failed: ' + (data.error || 'Unknown error'));
                }
            } catch (error) {
                log('✗ Error: ' + error.message);
            }
        }

        // Add manual trigger functions for testing
        window.triggerBanner = function(type, message) {
            document.dispatchEvent(new CustomEvent('showNotificationBanner', {
                detail: { type: type, message: message }
            }));
        };
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                if (window.notificationBanner) {
                    log('✓ Banner system initialized');
                    document.getElementById('user-type').textContent = 
                        window.notificationBanner.isAdmin ? 'Admin' : 'User';
                } else {
                    log('✗ Banner system failed to initialize');
                    document.getElementById('user-type').textContent = 'Unknown';
                }
            }, 1000);
        });
    </script>
</body>
</html>

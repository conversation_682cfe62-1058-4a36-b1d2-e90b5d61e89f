<?php
/**
 * LendSwift Production Installer
 * 
 * Secure installer for the LendSwift Loan Management System
 * Features: Database configuration, security validation, system verification
 * 
 * @package LendSwift
 * @version 1.0
 * <AUTHOR> Team
 */

// Prevent direct access if already installed
if (file_exists('../includes/core/.installed')) {
    die('Installation already completed. Please remove the installer directory for security.');
}

// Start session for installer state management
session_start();

// Configuration
define('INSTALLER_VERSION', '1.0');
define('REQUIRED_PHP_VERSION', '7.4');
define('CONFIG_FILE_PATH', '../includes/core/config.php');
define('SQL_DIRECTORY', '../sql');
define('UPLOADS_DIRECTORY', '../uploads');

// Installation steps
$steps = [
    1 => 'System Requirements',
    2 => 'Security Validation', 
    3 => 'Database Configuration',
    4 => 'Database Installation',
    5 => 'System Verification',
    6 => 'Installation Complete'
];

// Get current step
$current_step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$current_step = max(1, min(6, $current_step));

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    handleFormSubmission();
}

/**
 * Handle form submissions for each step
 */
function handleFormSubmission() {
    global $current_step;
    
    switch ($current_step) {
        case 2:
            handleSecurityValidation();
            break;
        case 3:
            handleDatabaseConfiguration();
            break;
        case 4:
            handleDatabaseInstallation();
            break;
        case 5:
            handleSystemVerification();
            break;
    }
}

/**
 * Handle security key validation
 */
function handleSecurityValidation() {
    $provided_key = $_POST['security_key'] ?? '';
    $expected_key = generateSecurityKey();
    
    if (hash_equals($expected_key, $provided_key)) {
        $_SESSION['security_validated'] = true;
        $_SESSION['install_token'] = bin2hex(random_bytes(32));
        redirect(3);
    } else {
        $_SESSION['error'] = 'Invalid security key. Please check your key and try again.';
    }
}

/**
 * Handle database configuration
 */
function handleDatabaseConfiguration() {
    if (!isSecurityValidated()) {
        redirect(2);
        return;
    }
    
    $db_config = [
        'host' => $_POST['db_host'] ?? '',
        'user' => $_POST['db_user'] ?? '',
        'pass' => $_POST['db_pass'] ?? '',
        'name' => $_POST['db_name'] ?? ''
    ];
    
    // Test database connection
    $connection_result = testDatabaseConnection($db_config);
    
    if ($connection_result['success']) {
        // Update config file
        if (updateConfigFile($db_config)) {
            $_SESSION['db_config'] = $db_config;
            $_SESSION['success'] = 'Database configuration updated successfully.';
            redirect(4);
        } else {
            $_SESSION['error'] = 'Failed to update configuration file. Please check file permissions.';
        }
    } else {
        $_SESSION['error'] = 'Database connection failed: ' . $connection_result['error'];
    }
}

/**
 * Handle database installation
 */
function handleDatabaseInstallation() {
    if (!isSecurityValidated() || !isset($_SESSION['db_config'])) {
        redirect(2);
        return;
    }
    
    $result = installDatabase();
    
    if ($result['success']) {
        $_SESSION['success'] = 'Database installed successfully.';
        redirect(5);
    } else {
        $_SESSION['error'] = 'Database installation failed: ' . $result['error'];
    }
}

/**
 * Handle system verification
 */
function handleSystemVerification() {
    if (!isSecurityValidated()) {
        redirect(2);
        return;
    }
    
    $verification_results = performSystemVerification();
    $_SESSION['verification_results'] = $verification_results;
    
    if ($verification_results['overall_status']) {
        // Mark installation as complete
        file_put_contents('../includes/core/.installed', date('Y-m-d H:i:s'));
        redirect(6);
    } else {
        $_SESSION['error'] = 'System verification failed. Please check the issues below.';
    }
}

/**
 * Generate security key based on server environment
 */
function generateSecurityKey() {
    $server_signature = $_SERVER['SERVER_NAME'] . $_SERVER['DOCUMENT_ROOT'] . php_uname();
    return substr(hash('sha256', $server_signature . 'LENDSWIFT_INSTALL'), 0, 16);
}

/**
 * Check if security validation passed
 */
function isSecurityValidated() {
    return isset($_SESSION['security_validated']) && $_SESSION['security_validated'] === true;
}

/**
 * Scan existing config file for database settings
 */
function scanExistingConfig() {
    if (!file_exists(CONFIG_FILE_PATH)) {
        return null;
    }
    
    $config_content = file_get_contents(CONFIG_FILE_PATH);
    $config = [];
    
    // Extract database configuration
    if (preg_match("/define\('DB_HOST',\s*'([^']+)'\)/", $config_content, $matches)) {
        $config['host'] = $matches[1];
    }
    if (preg_match("/define\('DB_USER',\s*'([^']+)'\)/", $config_content, $matches)) {
        $config['user'] = $matches[1];
    }
    if (preg_match("/define\('DB_NAME',\s*'([^']+)'\)/", $config_content, $matches)) {
        $config['name'] = $matches[1];
    }
    
    return $config;
}

/**
 * Test database connection
 */
function testDatabaseConnection($config) {
    try {
        $dsn = "mysql:host={$config['host']};charset=utf8mb4";
        $pdo = new PDO($dsn, $config['user'], $config['pass'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
        
        // Try to create database if it doesn't exist
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$config['name']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        
        // Test connection to the specific database
        $dsn = "mysql:host={$config['host']};dbname={$config['name']};charset=utf8mb4";
        $pdo = new PDO($dsn, $config['user'], $config['pass']);
        
        return ['success' => true, 'message' => 'Database connection successful'];
    } catch (PDOException $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Update configuration file with new database settings
 */
function updateConfigFile($config) {
    if (!file_exists(CONFIG_FILE_PATH)) {
        return false;
    }
    
    $config_content = file_get_contents(CONFIG_FILE_PATH);
    
    // Update database configuration
    $config_content = preg_replace(
        "/define\('DB_HOST',\s*'[^']+'\)/",
        "define('DB_HOST', '{$config['host']}')",
        $config_content
    );
    $config_content = preg_replace(
        "/define\('DB_USER',\s*'[^']+'\)/",
        "define('DB_USER', '{$config['user']}')",
        $config_content
    );
    $config_content = preg_replace(
        "/define\('DB_PASS',\s*'[^']+'\)/",
        "define('DB_PASS', '{$config['pass']}')",
        $config_content
    );
    $config_content = preg_replace(
        "/define\('DB_NAME',\s*'[^']+'\)/",
        "define('DB_NAME', '{$config['name']}')",
        $config_content
    );
    
    // Set development mode to false for production
    $config_content = preg_replace(
        "/define\('DEVELOPMENT_MODE',\s*true\)/",
        "define('DEVELOPMENT_MODE', false)",
        $config_content
    );
    
    return file_put_contents(CONFIG_FILE_PATH, $config_content) !== false;
}

/**
 * Install database from SQL files
 */
function installDatabase() {
    try {
        $config = $_SESSION['db_config'];
        $dsn = "mysql:host={$config['host']};dbname={$config['name']};charset=utf8mb4";
        $pdo = new PDO($dsn, $config['user'], $config['pass'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        ]);
        
        // Get SQL files in order
        $sql_files = [
            'schema.sql',
            'initial_data.sql'
        ];
        
        foreach ($sql_files as $file) {
            $file_path = SQL_DIRECTORY . '/' . $file;
            if (file_exists($file_path)) {
                $sql = file_get_contents($file_path);
                $pdo->exec($sql);
            }
        }
        
        return ['success' => true, 'message' => 'Database installed successfully'];
    } catch (Exception $e) {
        return ['success' => false, 'error' => $e->getMessage()];
    }
}

/**
 * Perform comprehensive system verification
 */
function performSystemVerification() {
    $results = [
        'file_integrity' => checkFileIntegrity(),
        'database_structure' => checkDatabaseStructure(),
        'permissions' => checkPermissions(),
        'php_requirements' => checkPhpRequirements()
    ];
    
    $results['overall_status'] = $results['file_integrity']['status'] && 
                                $results['database_structure']['status'] && 
                                $results['permissions']['status'] && 
                                $results['php_requirements']['status'];
    
    return $results;
}

/**
 * Check file integrity
 */
function checkFileIntegrity() {
    $required_files = [
        '../index.php',
        '../includes/init.php',
        '../includes/core/config.php',
        '../includes/core/functions.php',
        '../admin/index.php',
        '../admin/login.php'
    ];
    
    $missing_files = [];
    foreach ($required_files as $file) {
        if (!file_exists($file)) {
            $missing_files[] = $file;
        }
    }
    
    return [
        'status' => empty($missing_files),
        'missing_files' => $missing_files,
        'total_checked' => count($required_files)
    ];
}

/**
 * Check database structure
 */
function checkDatabaseStructure() {
    try {
        $config = $_SESSION['db_config'];
        $dsn = "mysql:host={$config['host']};dbname={$config['name']};charset=utf8mb4";
        $pdo = new PDO($dsn, $config['user'], $config['pass']);
        
        // Check for required tables
        $required_tables = [
            'users', 'admins', 'loan_applications', 'loan_products', 
            'loan_statuses', 'notifications', 'admin_notifications'
        ];
        
        $existing_tables = [];
        $stmt = $pdo->query("SHOW TABLES");
        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $existing_tables[] = $row[0];
        }
        
        $missing_tables = array_diff($required_tables, $existing_tables);
        
        return [
            'status' => empty($missing_tables),
            'existing_tables' => $existing_tables,
            'missing_tables' => $missing_tables,
            'total_required' => count($required_tables)
        ];
    } catch (Exception $e) {
        return [
            'status' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * Check directory permissions
 */
function checkPermissions() {
    $directories = [
        '../uploads' => 'writable',
        '../uploads/documents' => 'writable',
        '../uploads/receipts' => 'writable',
        '../includes/core' => 'writable'
    ];
    
    $permission_issues = [];
    foreach ($directories as $dir => $required_permission) {
        if (!file_exists($dir)) {
            mkdir($dir, 0755, true);
        }
        
        if ($required_permission === 'writable' && !is_writable($dir)) {
            $permission_issues[] = $dir;
        }
    }
    
    return [
        'status' => empty($permission_issues),
        'issues' => $permission_issues,
        'directories_checked' => array_keys($directories)
    ];
}

/**
 * Check PHP requirements
 */
function checkPhpRequirements() {
    $requirements = [
        'php_version' => version_compare(PHP_VERSION, REQUIRED_PHP_VERSION, '>='),
        'pdo_mysql' => extension_loaded('pdo_mysql'),
        'mbstring' => extension_loaded('mbstring'),
        'openssl' => extension_loaded('openssl'),
        'curl' => extension_loaded('curl')
    ];
    
    $all_met = array_reduce($requirements, function($carry, $item) {
        return $carry && $item;
    }, true);
    
    return [
        'status' => $all_met,
        'requirements' => $requirements,
        'php_version' => PHP_VERSION
    ];
}

/**
 * Redirect to specific step
 */
function redirect($step) {
    header("Location: install.php?step=$step");
    exit;
}

/**
 * Get flash message and clear it
 */
function getFlashMessage($type) {
    if (isset($_SESSION[$type])) {
        $message = $_SESSION[$type];
        unset($_SESSION[$type]);
        return $message;
    }
    return null;
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LendSwift Installation - Step <?php echo $current_step; ?></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .installer-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 800px;
            overflow: hidden;
        }
        
        .installer-header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .installer-header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .installer-header p {
            opacity: 0.9;
            font-size: 16px;
        }
        
        .progress-bar {
            background: #ecf0f1;
            height: 6px;
            margin-top: 20px;
            border-radius: 3px;
            overflow: hidden;
        }
        
        .progress-fill {
            background: #3498db;
            height: 100%;
            width: <?php echo ($current_step / 6) * 100; ?>%;
            transition: width 0.3s ease;
        }
        
        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
            font-size: 12px;
        }
        
        .step {
            opacity: 0.6;
            text-align: center;
            flex: 1;
        }
        
        .step.active {
            opacity: 1;
            font-weight: bold;
        }
        
        .step.completed {
            opacity: 0.8;
            color: #27ae60;
        }
        
        .installer-content {
            padding: 40px;
        }
        
        .alert {
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            border-left: 4px solid;
        }
        
        .alert-success {
            background: #d4edda;
            border-color: #27ae60;
            color: #155724;
        }
        
        .alert-error {
            background: #f8d7da;
            border-color: #e74c3c;
            color: #721c24;
        }
        
        .alert-info {
            background: #d1ecf1;
            border-color: #3498db;
            color: #0c5460;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ecf0f1;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #3498db;
        }
        
        .btn {
            background: #3498db;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: background 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            background: #2980b9;
        }
        
        .btn-success {
            background: #27ae60;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .verification-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .verification-card {
            border: 1px solid #ecf0f1;
            border-radius: 8px;
            padding: 20px;
        }
        
        .verification-card h4 {
            margin-bottom: 15px;
            color: #2c3e50;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .security-key-display {
            background: #f8f9fa;
            border: 2px dashed #6c757d;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin: 20px 0;
        }
        
        .security-key {
            font-family: 'Courier New', monospace;
            font-size: 24px;
            font-weight: bold;
            color: #e74c3c;
            letter-spacing: 2px;
            margin: 10px 0;
        }
        
        .completion-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            margin-top: 20px;
        }
        
        .completion-info h4 {
            color: #27ae60;
            margin-bottom: 15px;
        }
        
        .credential-box {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .credential-box strong {
            color: #2c3e50;
        }
        
        .text-center {
            text-align: center;
        }
        
        .mt-20 {
            margin-top: 20px;
        }
        
        .mb-20 {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="installer-container">
        <div class="installer-header">
            <h1>🚀 LendSwift Installation</h1>
            <p>Production Installer v<?php echo INSTALLER_VERSION; ?></p>

            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>

            <div class="step-indicator">
                <?php foreach ($steps as $step_num => $step_name): ?>
                    <div class="step <?php echo $step_num == $current_step ? 'active' : ($step_num < $current_step ? 'completed' : ''); ?>">
                        <?php echo $step_num; ?>. <?php echo $step_name; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <div class="installer-content">
            <?php
            // Display flash messages
            if ($success = getFlashMessage('success')): ?>
                <div class="alert alert-success">✅ <?php echo htmlspecialchars($success); ?></div>
            <?php endif; ?>

            <?php if ($error = getFlashMessage('error')): ?>
                <div class="alert alert-error">❌ <?php echo htmlspecialchars($error); ?></div>
            <?php endif; ?>

            <?php
            // Render current step
            switch ($current_step) {
                case 1:
                    renderSystemRequirements();
                    break;
                case 2:
                    renderSecurityValidation();
                    break;
                case 3:
                    renderDatabaseConfiguration();
                    break;
                case 4:
                    renderDatabaseInstallation();
                    break;
                case 5:
                    renderSystemVerification();
                    break;
                case 6:
                    renderInstallationComplete();
                    break;
            }
            ?>
        </div>
    </div>

    <script>
        // Auto-refresh for installation steps
        function autoRefresh(seconds) {
            setTimeout(function() {
                window.location.reload();
            }, seconds * 1000);
        }

        // Copy to clipboard functionality
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                alert('Copied to clipboard!');
            });
        }

        // Form validation
        function validateForm(formId) {
            const form = document.getElementById(formId);
            const inputs = form.querySelectorAll('input[required]');
            let isValid = true;

            inputs.forEach(function(input) {
                if (!input.value.trim()) {
                    input.style.borderColor = '#e74c3c';
                    isValid = false;
                } else {
                    input.style.borderColor = '#ecf0f1';
                }
            });

            return isValid;
        }
    </script>
</body>
</html>

<?php
/**
 * Step rendering functions
 */

/**
 * Render system requirements step
 */
function renderSystemRequirements() {
    $requirements = checkPhpRequirements();
    ?>
    <h2>📋 System Requirements Check</h2>
    <p>Let's verify that your server meets the requirements for LendSwift.</p>

    <div class="verification-grid">
        <div class="verification-card">
            <h4>PHP Requirements</h4>
            <div class="status-item">
                <span>PHP Version (>= <?php echo REQUIRED_PHP_VERSION; ?>)</span>
                <span class="status-badge <?php echo $requirements['requirements']['php_version'] ? 'status-success' : 'status-error'; ?>">
                    <?php echo $requirements['php_version']; ?>
                </span>
            </div>
            <div class="status-item">
                <span>PDO MySQL Extension</span>
                <span class="status-badge <?php echo $requirements['requirements']['pdo_mysql'] ? 'status-success' : 'status-error'; ?>">
                    <?php echo $requirements['requirements']['pdo_mysql'] ? 'Available' : 'Missing'; ?>
                </span>
            </div>
            <div class="status-item">
                <span>Mbstring Extension</span>
                <span class="status-badge <?php echo $requirements['requirements']['mbstring'] ? 'status-success' : 'status-error'; ?>">
                    <?php echo $requirements['requirements']['mbstring'] ? 'Available' : 'Missing'; ?>
                </span>
            </div>
            <div class="status-item">
                <span>OpenSSL Extension</span>
                <span class="status-badge <?php echo $requirements['requirements']['openssl'] ? 'status-success' : 'status-error'; ?>">
                    <?php echo $requirements['requirements']['openssl'] ? 'Available' : 'Missing'; ?>
                </span>
            </div>
            <div class="status-item">
                <span>cURL Extension</span>
                <span class="status-badge <?php echo $requirements['requirements']['curl'] ? 'status-success' : 'status-error'; ?>">
                    <?php echo $requirements['requirements']['curl'] ? 'Available' : 'Missing'; ?>
                </span>
            </div>
        </div>

        <div class="verification-card">
            <h4>File System</h4>
            <?php $permissions = checkPermissions(); ?>
            <?php foreach ($permissions['directories_checked'] as $dir): ?>
                <div class="status-item">
                    <span><?php echo $dir; ?></span>
                    <span class="status-badge <?php echo !in_array($dir, $permissions['issues']) ? 'status-success' : 'status-error'; ?>">
                        <?php echo !in_array($dir, $permissions['issues']) ? 'Writable' : 'Not Writable'; ?>
                    </span>
                </div>
            <?php endforeach; ?>
        </div>
    </div>

    <?php if ($requirements['status'] && $permissions['status']): ?>
        <div class="alert alert-success mt-20">
            ✅ All system requirements are met! You can proceed with the installation.
        </div>
        <div class="text-center mt-20">
            <a href="install.php?step=2" class="btn btn-success">Continue to Security Validation →</a>
        </div>
    <?php else: ?>
        <div class="alert alert-error mt-20">
            ❌ Some requirements are not met. Please resolve the issues above before continuing.
        </div>
        <div class="text-center mt-20">
            <a href="install.php?step=1" class="btn">🔄 Recheck Requirements</a>
        </div>
    <?php endif; ?>
    <?php
}

/**
 * Render security validation step
 */
function renderSecurityValidation() {
    $security_key = generateSecurityKey();
    ?>
    <h2>🔐 Security Validation</h2>
    <p>For security purposes, please enter the installation key generated for your server.</p>

    <div class="security-key-display">
        <h4>Your Installation Key:</h4>
        <div class="security-key" onclick="copyToClipboard('<?php echo $security_key; ?>')" title="Click to copy">
            <?php echo $security_key; ?>
        </div>
        <small>Click the key above to copy it to your clipboard</small>
    </div>

    <div class="alert alert-info">
        <strong>How this key is generated:</strong><br>
        This key is uniquely generated based on your server's hostname, document root, and system information.
        It ensures that only someone with access to this server can complete the installation.
    </div>

    <form method="POST" id="securityForm">
        <div class="form-group">
            <label for="security_key">Enter Installation Key:</label>
            <input type="text" id="security_key" name="security_key" required
                   placeholder="Enter the installation key shown above"
                   pattern="[A-Fa-f0-9]{16}"
                   title="Please enter the 16-character hexadecimal key">
        </div>

        <div class="text-center">
            <button type="submit" class="btn" onclick="return validateForm('securityForm')">
                🔓 Validate Key & Continue
            </button>
        </div>
    </form>
    <?php
}

/**
 * Render database configuration step
 */
function renderDatabaseConfiguration() {
    if (!isSecurityValidated()) {
        echo '<div class="alert alert-error">❌ Security validation required. Please complete the previous step.</div>';
        echo '<div class="text-center"><a href="install.php?step=2" class="btn">← Back to Security Validation</a></div>';
        return;
    }

    $existing_config = scanExistingConfig();
    ?>
    <h2>🗄️ Database Configuration</h2>
    <p>Configure your database connection settings. We'll test the connection before proceeding.</p>

    <?php if ($existing_config): ?>
        <div class="alert alert-info">
            📋 We found existing database configuration in your config file. You can modify it below or keep the current settings.
        </div>
    <?php endif; ?>

    <form method="POST" id="databaseForm">
        <div class="form-group">
            <label for="db_host">Database Host:</label>
            <input type="text" id="db_host" name="db_host" required
                   value="<?php echo htmlspecialchars($existing_config['host'] ?? 'localhost'); ?>"
                   placeholder="localhost">
        </div>

        <div class="form-group">
            <label for="db_user">Database Username:</label>
            <input type="text" id="db_user" name="db_user" required
                   value="<?php echo htmlspecialchars($existing_config['user'] ?? ''); ?>"
                   placeholder="Database username">
        </div>

        <div class="form-group">
            <label for="db_pass">Database Password:</label>
            <input type="password" id="db_pass" name="db_pass"
                   placeholder="Database password">
        </div>

        <div class="form-group">
            <label for="db_name">Database Name:</label>
            <input type="text" id="db_name" name="db_name" required
                   value="<?php echo htmlspecialchars($existing_config['name'] ?? 'loan'); ?>"
                   placeholder="loan">
        </div>

        <div class="alert alert-info">
            <strong>Note:</strong> If the database doesn't exist, we'll try to create it for you.
        </div>

        <div class="text-center">
            <button type="submit" class="btn" onclick="return validateForm('databaseForm')">
                🔌 Test Connection & Continue
            </button>
        </div>
    </form>
    <?php
}

/**
 * Render database installation step
 */
function renderDatabaseInstallation() {
    if (!isSecurityValidated() || !isset($_SESSION['db_config'])) {
        echo '<div class="alert alert-error">❌ Database configuration required. Please complete the previous steps.</div>';
        echo '<div class="text-center"><a href="install.php?step=3" class="btn">← Back to Database Configuration</a></div>';
        return;
    }
    ?>
    <h2>📦 Database Installation</h2>
    <p>Installing the LendSwift database structure and initial data...</p>

    <div class="alert alert-info">
        🔄 Installing database tables and initial data. This may take a few moments...
    </div>

    <form method="POST">
        <div class="text-center">
            <button type="submit" class="btn btn-success">
                🚀 Install Database
            </button>
        </div>
    </form>

    <script>
        // Auto-submit the form after 2 seconds
        setTimeout(function() {
            document.querySelector('form').submit();
        }, 2000);
    </script>
    <?php
}

/**
 * Render system verification step
 */
function renderSystemVerification() {
    if (!isSecurityValidated()) {
        echo '<div class="alert alert-error">❌ Security validation required. Please complete the previous steps.</div>';
        echo '<div class="text-center"><a href="install.php?step=2" class="btn">← Back to Security Validation</a></div>';
        return;
    }

    $verification_results = isset($_SESSION['verification_results']) ? $_SESSION['verification_results'] : performSystemVerification();
    ?>
    <h2>🔍 System Verification</h2>
    <p>Performing final system checks to ensure everything is properly installed.</p>

    <div class="verification-grid">
        <!-- File Integrity Check -->
        <div class="verification-card">
            <h4>📁 File Integrity</h4>
            <div class="status-item">
                <span>Required Files</span>
                <span class="status-badge <?php echo $verification_results['file_integrity']['status'] ? 'status-success' : 'status-error'; ?>">
                    <?php echo $verification_results['file_integrity']['status'] ? 'All Present' : 'Missing Files'; ?>
                </span>
            </div>
            <?php if (!empty($verification_results['file_integrity']['missing_files'])): ?>
                <div class="alert alert-error">
                    Missing files: <?php echo implode(', ', $verification_results['file_integrity']['missing_files']); ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Database Structure Check -->
        <div class="verification-card">
            <h4>🗄️ Database Structure</h4>
            <div class="status-item">
                <span>Required Tables</span>
                <span class="status-badge <?php echo $verification_results['database_structure']['status'] ? 'status-success' : 'status-error'; ?>">
                    <?php echo $verification_results['database_structure']['status'] ? 'All Created' : 'Missing Tables'; ?>
                </span>
            </div>
            <?php if (isset($verification_results['database_structure']['existing_tables'])): ?>
                <small>Found <?php echo count($verification_results['database_structure']['existing_tables']); ?> tables</small>
            <?php endif; ?>
            <?php if (!empty($verification_results['database_structure']['missing_tables'])): ?>
                <div class="alert alert-error">
                    Missing tables: <?php echo implode(', ', $verification_results['database_structure']['missing_tables']); ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- Permissions Check -->
        <div class="verification-card">
            <h4>🔐 Permissions</h4>
            <div class="status-item">
                <span>Directory Permissions</span>
                <span class="status-badge <?php echo $verification_results['permissions']['status'] ? 'status-success' : 'status-error'; ?>">
                    <?php echo $verification_results['permissions']['status'] ? 'Correct' : 'Issues Found'; ?>
                </span>
            </div>
            <?php if (!empty($verification_results['permissions']['issues'])): ?>
                <div class="alert alert-error">
                    Permission issues: <?php echo implode(', ', $verification_results['permissions']['issues']); ?>
                </div>
            <?php endif; ?>
        </div>

        <!-- PHP Requirements Check -->
        <div class="verification-card">
            <h4>🐘 PHP Requirements</h4>
            <div class="status-item">
                <span>All Requirements</span>
                <span class="status-badge <?php echo $verification_results['php_requirements']['status'] ? 'status-success' : 'status-error'; ?>">
                    <?php echo $verification_results['php_requirements']['status'] ? 'Met' : 'Not Met'; ?>
                </span>
            </div>
        </div>
    </div>

    <?php if ($verification_results['overall_status']): ?>
        <div class="alert alert-success mt-20">
            ✅ All system verification checks passed! Your LendSwift installation is ready.
        </div>
        <form method="POST">
            <div class="text-center mt-20">
                <button type="submit" class="btn btn-success">
                    🎉 Complete Installation
                </button>
            </div>
        </form>
    <?php else: ?>
        <div class="alert alert-error mt-20">
            ❌ Some verification checks failed. Please resolve the issues above before completing the installation.
        </div>
        <div class="text-center mt-20">
            <a href="install.php?step=5" class="btn">🔄 Re-run Verification</a>
        </div>
    <?php endif; ?>
    <?php
}

/**
 * Render installation complete step
 */
function renderInstallationComplete() {
    ?>
    <h2>🎉 Installation Complete!</h2>
    <p>Congratulations! LendSwift has been successfully installed on your server.</p>

    <div class="completion-info">
        <h4>🚀 Your LendSwift Installation is Ready</h4>

        <div class="credential-box">
            <h5>📱 User Interface</h5>
            <strong>URL:</strong> <a href="../index.php" target="_blank"><?php echo getCurrentBaseUrl(); ?></a><br>
            <small>This is where your customers will access the loan application system.</small>
        </div>

        <div class="credential-box">
            <h5>⚙️ Admin Panel</h5>
            <strong>URL:</strong> <a href="../admin/" target="_blank"><?php echo getCurrentBaseUrl(); ?>/admin/</a><br>
            <strong>Username:</strong> <code><EMAIL></code><br>
            <strong>Password:</strong> <code>admin123</code><br>
            <small>⚠️ Please change these default credentials immediately after logging in!</small>
        </div>

        <div class="credential-box">
            <h5>📊 System Information</h5>
            <strong>Installation Date:</strong> <?php echo date('Y-m-d H:i:s'); ?><br>
            <strong>PHP Version:</strong> <?php echo PHP_VERSION; ?><br>
            <strong>Database:</strong> <?php echo $_SESSION['db_config']['name'] ?? 'N/A'; ?><br>
            <strong>Installation Key:</strong> <code><?php echo generateSecurityKey(); ?></code>
        </div>
    </div>

    <div class="alert alert-info">
        <strong>🔒 Security Recommendations:</strong>
        <ul style="margin: 10px 0; padding-left: 20px;">
            <li>Change the default admin password immediately</li>
            <li>Remove or secure the installer directory</li>
            <li>Configure SSL/HTTPS for production use</li>
            <li>Set up regular database backups</li>
            <li>Review and configure email settings in the admin panel</li>
        </ul>
    </div>

    <div class="alert alert-success">
        <strong>📚 Next Steps:</strong>
        <ol style="margin: 10px 0; padding-left: 20px;">
            <li>Log in to the admin panel and change default credentials</li>
            <li>Configure your company information and settings</li>
            <li>Set up email templates and SMTP settings</li>
            <li>Create loan products and configure interest rates</li>
            <li>Test the user registration and loan application process</li>
        </ol>
    </div>

    <div class="text-center mt-20">
        <a href="../admin/" class="btn btn-success" target="_blank">
            🎯 Go to Admin Panel
        </a>
        <a href="../index.php" class="btn" target="_blank">
            👥 View User Interface
        </a>
    </div>

    <div class="text-center mt-20">
        <button onclick="cleanupInstaller()" class="btn" style="background: #e74c3c;">
            🗑️ Remove Installer (Recommended)
        </button>
    </div>

    <script>
        function cleanupInstaller() {
            if (confirm('Are you sure you want to remove the installer? This action cannot be undone.')) {
                fetch('cleanup.php', {method: 'POST'})
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('Installer removed successfully!');
                            window.location.href = '../admin/';
                        } else {
                            alert('Failed to remove installer: ' + data.error);
                        }
                    })
                    .catch(error => {
                        alert('Error: ' + error);
                    });
            }
        }
    </script>
    <?php
}

/**
 * Get current base URL
 */
function getCurrentBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $path = dirname(dirname($_SERVER['REQUEST_URI']));
    return $protocol . '://' . $host . $path;
}

?>
